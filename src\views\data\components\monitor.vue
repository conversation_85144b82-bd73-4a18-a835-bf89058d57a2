<script setup>
  import bussinessApi from '@/api/bussiness/bussiness.api';
  import dataApi from '@/api/data/data.api';
  import { ref, onMounted, defineProps, watch, onUnmounted } from 'vue'

  const props = defineProps({
    tableName: { required: true }
  });

  // 新增定时器引用
  const timer = ref(null);
  const tableHeightStatistics = ref(0)
  const tableHeight = ref(0)
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 30,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,

  });
  const dwdPagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['10', '20', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
  });
  const activeKey = ref('1')
  const columnsStatistics = ref([
    {
      title: '序号',
      key: 'index',
      width: 60,
      align: 'center',
      fixed: 'left',
      customRender: ({ text, record, index }) => {
        return index + 1;
      },
    },
    // {
    //   title: '时间戳',
    //   dataIndex: 'time',
    //   key: 'time',
    // },
    {
      title: '指令',
      dataIndex: 'zl',
      key: 'zl',
      width: 120,
      ellipsis: true
    },
    {
      title: '指令名称',
      dataIndex: 'zlmc',
      key: 'zlmc',
      width: 200,
      ellipsis: true
    },

    {
      title: '数据总数',
      dataIndex: 'total',
      key: 'total',
      width: 120,
      align: 'center'
    }
  ])
  const columns = ref([])
  const tableData = ref([])
  const compilationTableData = ref([])
  const dataStatistics = ref([{ tableName: '-', total: '-' }])

  function getTopicStatistic(tableName) {

    const params = {
      pagingSort: {
        currentPage: pagination.value.current,
        pageSize: pagination.value.pageSize
      },
      tableName: tableName
    }

    //获取原始库数据
    dataApi.getSSBWPageTable(params).then(res => {

      const { pageData } = res?.data
      const [zl, zlmc] = tableName.split('_')
      console.log('pageData', pageData);

      dataStatistics.value = [{ zlmc: zlmc, zl: zl, total: pageData.total }]
      const arr = [{
        title: '序号',
        key: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ text, record, index }) => {
          return index + 1;
        },
      }]
      for (const col of res.data.columns) {
        // 根据列名和注释设置合适的宽度
        let width = 150; // 默认宽度
        let align = 'left';
        
        // 根据列名特征设置宽度
        const columnName = col.columnName.toLowerCase();
        const comment = col.comment || '';
        
        if (columnName.includes('time') || columnName.includes('date') || comment.includes('时间') || comment.includes('日期')) {
          width = 160;
        } else if (columnName.includes('id') || columnName.includes('num') || comment.includes('编号') || comment.includes('数量')) {
          width = 100;
          align = 'center';
        } else if (columnName.includes('name') || columnName.includes('title') || comment.includes('名称') || comment.includes('标题')) {
          width = 180;
        } else if (columnName.includes('status') || columnName.includes('state') || comment.includes('状态')) {
          width = 100;
          align = 'center';
        } else if (columnName.includes('size') || comment.includes('大小')) {
          width = 100;
          align = 'center';
        }
        
        arr.push({
          title: col.comment,
          key: col.columnName,
          width: width,
          align: align,
          ellipsis: true,
          dataIndex: col.columnName
        })
      }
      columns.value = arr
      tableData.value = pageData.records // 原始库
      pagination.value.total = pageData.total

      // 获取整编库数据，使用dwd_前缀
      const dwdParams = {
        pagingSort: {
          currentPage: dwdPagination.value.current,
          pageSize: dwdPagination.value.pageSize
        },
        tableName: `dwd_${tableName}`
      }
      dataApi.getSSBWPageTable(dwdParams).then(dwdRes => {
        if (dwdRes?.data && dwdRes.data.pageData) {
          compilationTableData.value = dwdRes.data.pageData.records // 整编库
          dwdPagination.value.total = dwdRes.data.pageData.total
        } else {
          compilationTableData.value = [] // 如果没有数据则设为空数组
        }
      }).catch(err => {
        console.error("获取整编库数据失败:", err)
        compilationTableData.value = [] // 请求失败时设为空数组
      })
    })
  }
  const changePagination = (pageInfo) => {
    console.log('pageInfo.current', pageInfo.current)
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    getTopicStatistic(props.tableName)
  }
  const changeDwdPagination = (pageInfo) => {
    dwdPagination.value.current = pageInfo.current;
    dwdPagination.value.pageSize = pageInfo.pageSize;
    getTopicStatistic(props.tableName)
  }
  // 组件卸载时清除定时器
  onUnmounted(() => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  })
  watch(() => props.tableName, (val) => {
    if (val) {
      getTopicStatistic(val)
    }
  }, { immediate: true })

  onMounted(() => {
    // 初始化定时器
    timer.value = setInterval(() => {
      getTopicStatistic(props.tableName)
    }, 3000)

    window.onresize = () => {
      tableHeight.value = document.getElementsByClassName('detail')[0].offsetHeight - 126;
      tableHeightStatistics.value = document.getElementsByClassName('detail')[0].offsetHeight - 40;
    };
    tableHeight.value = document.getElementsByClassName('detail')[0].offsetHeight - 126;
    tableHeightStatistics.value = document.getElementsByClassName('detail')[0].offsetHeight - 40;
  });


</script>
<template>
  <div class="statistics">
    <div class="tableTitle">接收数据统计</div>
    <a-table class="detail-table-component" :columns="columnsStatistics" :data-source="dataStatistics"
      :pagination="false" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
      :scroll="{ y: tableHeightStatistics }"
      :style="{ height: dataStatistics.length === 0 ? tableHeightStatistics + 'px' : 'calc(100% - 38px)'}">
    </a-table>
  </div>
  <div class="detail">
    <div class="tableTitle">数据查看</div>
    <a-tabs v-model:activeKey="activeKey" size="small">
      <a-tab-pane key="1" tab="原始库">
        <a-table class="detail-table-component" :columns="columns" :data-source="tableData" :pagination="pagination"
          :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
          :scroll="{ y: tableHeight, x: columns.length*150 }" @change="changePagination">
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="2" tab="整编库">
        <a-table class="detail-table-component" :columns="columns" :data-source="compilationTableData"
          :pagination="dwdPagination" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
          :scroll="{ y: tableHeight, x: columns.length*150 }" @change="changeDwdPagination">
        </a-table>
      </a-tab-pane>
    </a-tabs>

  </div>
</template>
<style lang="less" scoped>
  @import '../sytle.less';

  :deep(.ant-tabs.ant-tabs-top .ant-tabs-tab) {
    padding: 0;
  }
</style>