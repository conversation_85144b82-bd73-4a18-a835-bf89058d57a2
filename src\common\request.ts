/**
 * @name 统一接口请求封装
 */

import axios, { AxiosInstance, AxiosPromise, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosRequestHeaders } from 'axios'
import { message, Modal, notification } from 'ant-design-vue'
import router from '@/router'
import { AxiosReturnTypes } from '@/types/common'
import ENV from '@/config'
import { default as commonApi } from '@/api/common/common.api'

/**
 * header 配置
 */
const contentType = 'application/json;charset=UTF-8'

/**
 * error message list
 */
const errMsg = {
  400: '错误请求',
  401: '未授权，请重新登录',
  403: '拒绝访问',
  404: '请求错误,未找到该资源',
  405: '请求方法未允许',
  408: '请求超时',
  500: '服务器端出错',
  501: '网络未实现',
  502: '网络错误',
  503: '服务不可用',
  504: '网络超时',
  505: 'http版本不支持该请求'
}

/**
 * @name 接口约束
 */
export interface AxiosRequestConfigIntl extends AxiosRequestConfig {
  message?: {
    /**
     * 是否显示消息窗，默认不显示，
     * @default false
     * @type boolean
     */
    noShow?: boolean
    /**
     * 自定义消息，
     * @default {}
     */
    options?: any // 自定义消息体
  }
}

/**
 * @name 主体控制
 */
class Request<T = AxiosReturnTypes> {
  axiosInstance: AxiosInstance
  public requestData: any = null

  constructor() {
    //创建axios实例
    this.axiosInstance = axios.create({
      baseURL: location.origin, // 公共接口-本地
      timeout: 40 * 1000, // 超时时间单位是ms
      withCredentials: true, // 身份验证
      headers: {
        'Content-Type': contentType,
        'X-Requested-With': 'XMLHttpRequest',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Authorization': localStorage.getItem("token"),
        'Token': localStorage.getItem("token"),
      }
    })

    //配置message窗口仅有一个
    message.config({
      maxCount: 1
    })

    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Axios的InternalAxiosRequestConfig保证headers存在
        // config.headers = config.headers || {}; // No longer needed due to InternalAxiosRequestConfig

        // 如果data是FormData的实例，则直接传递，不需要JSON.stringify
        // Axios会自动设置正确的Content-Type为multipart/form-data
        if (config.data instanceof FormData) {
          // 删除可能预设的Content-Type，确保Axios可以设置自己的
          // AxiosRequestHeaders can be safely indexed
          delete (config.headers as AxiosRequestHeaders)['Content-Type'];
        }
        // 如果Content-Type被显式设置为multipart/form-data（且data不是FormData实例），
        // 则尝试使用旧有的this.getFormData()逻辑（此逻辑可能存在this.requestData的时间差问题）
        else if (config.headers && (config.headers as AxiosRequestHeaders)['Content-Type'] === 'multipart/form-data') {
          config.data = this.getFormData();
        }
        // 其他情况（如application/json），如果存在data，则进行JSON.stringify
        else if (config.data) {
          config.data = JSON.stringify(config.data);
        }

        // 原有的GET请求处理逻辑 (config.query不是axios标准参数，可能是项目自定义用法)
        if (config.method === 'get' && config.data && typeof config.data === 'string') {
          (config as any).query = config.data;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    )

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response): AxiosPromise<AxiosReturnTypes> => {
        const { data, config } = response
        if (data.code && +data.code !== 200) {
          switch (data.code) {
            case 302: //登录失效code
              message.warn('登录失效')
              break
            case '401': //无权限code
              router.push({
                path: '/404'
              })
              break
            case '000000':
              this.onLogout()
              break
            case 13:
              break
            case 14:
              break
            default:
              if (data.data === 302) {
                Modal.confirm({
                  title: '登录过期',
                  content: '登录状态已过期，是否重新登录？',
                  onOk: () => { },
                  onCancel: () => { }
                })
              } else {
                notification.open({
                  type: 'error',
                  message: '操作失败',
                  description: data.msg || data.message,
                  duration: 2
                })
              }
              break
          }

          return Promise.reject(
            'okr请求异常拦截:' +
            JSON.stringify({
              url: config.url,
              code: data.code,
              data: data.data,
              msg: data.msg
            }) || 'Error'
          )
        } else {
          // 返回体改造，保持结构一致 -_-后端返回体不同意就该怼
          if (data.data?.list) {
            return Promise.resolve({
              ...data,
              result: data?.data?.list,
              totalCount: +data?.data?.total || +data?.data?.totalCount
            })
          }
          // 返回体改造，保持结构一致-_-后端返回体不同意就该怼
          if (data.data?.data) {
            return Promise.resolve({
              ...data,
              result: data?.data?.data,
              totalCount: +data?.data?.total || +data?.data?.totalCount
            })
          }

          return Promise.resolve(data)
        }
      },
      (error) => {
        // if (error && error.response) {
        //   error.message = errMsg[error.response.status] || `连接错误${error.response.status}`
        // } else {
        //   // 超时处理
        //   if (JSON.stringify(error).includes('timeout')) {
        //     //
        //     message.error((h) =>
        //       h('span', [
        //         h('span', '服务器响应超时，请刷新当前页，或'),
        //         h(
        //           'a',
        //           {
        //             style: 'margin-left:4px', // 样式内容
        //             on: {
        //               click: this.onLogout // 点击事件
        //             }
        //           },
        //           '重新登录'
        //         ),
        //         h('span', { style: 'margin-left:4px' }, '试试!')
        //       ])
        //     )
        //   } else {
        //     message.error(`连接服务器失败:${error.message}`)
        //   }
        // }
        // message.error(error.message);
        return Promise.reject(error)
      }
    )
  }

  /**
   * @name 注销
   */
  public async onLogout() {
    // localStorage.clear();
    // sessionStorage.clear();
    await commonApi.logOut()
    if (process.env.NODE_ENV !== 'production') {
      return (window.location.href = ENV.devLoginUrl)
    } else {
      return (window.location.href = ENV.proLoginUrl)
    }
  }

  // /**
  //  * @name 对参数进行formData处理【多层结构，递归处理】
  //  * @param data
  //  * @param formData
  //  * @param pre
  //  * @returns
  //  */
  // getFormData (data, formData, pre) {
  // 	if(Object.prototype.toString.call(data) === '[object Object]'){
  // 		const dataKeys = Object.keys(data);
  // 		for(let i=0, r=dataKeys.length; i<r; i++) {
  // 			if(pre === ''){
  // 				this.getFormData(data[dataKeys[i]], formData, `${dataKeys[i]}`);
  // 			}else{
  // 				this.getFormData(data[dataKeys[i]], formData, `${pre}[${dataKeys[i]}]`);
  // 			}
  // 		}
  // 	}else if(Object.prototype.toString.call(data) === '[object Array]'){
  // 		for(let j=0,k=data.length; j<k; j++){
  // 			this.getFormData(data[j], formData, `${pre}[${j}]`);
  // 		}
  // 	}else {
  // 		//console.log(`${pre}---------`, data);
  // 		formData.append(`${pre}`, data);
  // 	}
  // 	return formData;
  // }

  /**
   * @name 对参数进行formData处理
   * @param params
   * @returns
   */
  getFormData() {
    let formData = new FormData()
    const $this = this
    if ($this.requestData) {
      formData = Object.keys($this.requestData).reduce((arr, item, index) => {
        //二进制流文件 FormData处理
        if ($this.isFile($this.requestData[item])) {
          arr.append('file', $this.requestData[item])
          return arr
        } else if ($this.isFile($this.requestData[item]?.originFileObj)) {
          arr.append('file', $this.requestData[item].originFileObj)
          return arr
        }

        arr.append(item, $this.requestData[item])
        return arr
      }, formData)
    }
    return formData
  }

  /**
   * @name 判断是否是file object
   */
  isFile(params: Object) {
    return Object.prototype.toString.call(params) === '[object File]'
  }

  /**
   * @name get请求
   */
  get(url: string, params?: any, config?: AxiosRequestConfigIntl): Promise<T> {
    let result: any;
    // 如果params是对象，处理为URL查询参数
    if (params && typeof params === 'object' && !(params instanceof FormData)) {
      const queryArr = [];
      for (const key in params) {
        if (params.hasOwnProperty(key) && params[key] !== undefined && params[key] !== null) {
          queryArr.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
        }
      }
      if (queryArr.length) {
        url += (url.includes('?') ? '&' : '?') + queryArr.join('&');
      }
    }

    new Promise((resolve) => {
      result = this.axiosInstance.get(url, config);
      return resolve(result);
    }).then((res) => {
      return this.message({ config, res });
    });
    return Promise.resolve(result);
  }

  /**
   * @name post请求
   */
  post(url: string, data?: any, config?: AxiosRequestConfigIntl): Promise<T> {
    let result
    new Promise((resolve) => {
      result = this.axiosInstance.post(url, data, config)
      //TODO 获取请求参数
      this.requestData = data
      return resolve(result)
    }).then((res) => {
      this.message({ config, res })
    })

    return result
  }

  /**
   * @name put请求
   */
  put(url: string, data?: any, config?: AxiosRequestConfigIntl): Promise<T> {
    let result
    new Promise((resolve) => {
      result = this.axiosInstance.put(url, data, config)
      //TODO 获取请求参数
      this.requestData = data
      return resolve(result)
    }).then((res) => {
      this.message({ config, res })
    })
    return result
  }

  /**
   * @name patch请求
   */
  patch(url: string, data?: any, config?: AxiosRequestConfigIntl): Promise<T> {
    let result
    new Promise((resolve) => {
      result = this.axiosInstance.post(url, data, config)
      //TODO 获取请求参数
      this.requestData = data
      return resolve(result)
    }).then((res) => {
      this.message({ config, res })
    })
    return result
  }

  /**
   * @name delete请求
   */
  delete(url: string, config?: AxiosRequestConfigIntl): Promise<T> {
    let result
    new Promise((resolve) => {
      result = this.axiosInstance.delete(url, config)
      return resolve(result)
    }).then((res) => {
      this.message({ config, res })
    })
    return result
  }

  /**
   * @name 消息
   * @param data
   * @returns
   */
  message(data: { config: AxiosRequestConfigIntl; res: any }) {
    if (data?.config?.message?.noShow) return

    // 系统消息
    if (data?.config?.message?.options?.content) {
      return notification.open({
        type: data.config?.message?.options?.type || 'info',
        message: "提示",
        description: data.config.message.options.content,
        duration: data.config.message?.options?.duration || 2
      })
    }

    // 系统消息
    if (data?.res?.message) {
      // notification.open({
      //   type: 'info',
      //   message: "提示",
      //   description: data.res.message,
      //   duration: 2,
      // })
    }
  }
}
// 实例化
const request = new Request()
export default request
