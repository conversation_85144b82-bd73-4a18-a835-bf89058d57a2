import { ref } from 'vue';

export function useLoading() {
    const loading = ref(false);

    const setLoading = (status) => {
        loading.value = status;
    };

    const withLoading = async (asyncFn) => {
        const startTime = performance.now(); // 记录开始时间
        try {
            setLoading(true);
           
            await asyncFn();
        } finally {
            const endTime = performance.now();
            const requestTime = endTime - startTime;
            if (requestTime < 300) {
                const remainingTime = 300 - requestTime;
                setTimeout(() => {
                    setLoading(false);
                }, remainingTime);
            } else {
                // 请求时间超过300毫秒，立即结束loading
                setLoading(false);
            }
        }
    };

    return {
        loading,
        setLoading,
        withLoading,
    };
}
