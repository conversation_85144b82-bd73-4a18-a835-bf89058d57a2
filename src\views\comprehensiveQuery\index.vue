<script setup>
    import { ref, onMounted, computed, reactive, createVNode } from 'vue'
    import { Modal, message } from 'ant-design-vue'
    import {
        ExclamationCircleOutlined,
        UploadOutlined
    } from '@ant-design/icons-vue'
    import { tableMixin } from '@/mixins/table';
    import breadcrumb from '../home/<USER>/breadcrumb.vue';
    import fileApi from '@/api/file/file.api'
    import axios from 'axios'
    import bussinessApi from '@/api/bussiness/bussiness.api';
    import OrganizeApi from '@/api/organize/organize.api';
    import { STEP_OPTIONS } from './common/options.ts'
    const visible = ref(false)
    const type = ref('add')
    const filters = ref()
    const search = ref({})
    const columns = ref([
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align: 'center',
            width: 60,
            fixed: 'left'
        },
        {
            title: '文件全名称',
            dataIndex: 'fileName',
            key: 'fileName',
            width: 200,
            ellipsis: true
        }
        , 
        // {
        //     title: '分域',
        //     dataIndex: 'domainName',
        //     key: 'domainName',
        //     ellipsis: true,
        // },
        // {
        //     title: '系统',
        //     dataIndex: 'systemName',
        //     key: 'systemName',
        //     ellipsis: true
        // },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 160,
            ellipsis: true
        },
        {
            title: '上传人',
            dataIndex: 'username',
            key: 'username',
            width: 150,
            ellipsis: true
        },
        {
            title: '文件大小',
            dataIndex: 'fileSizeFormatted',
            key: 'fileSizeFormatted',
            width: 100,
            align: 'center'
        },
        {
            title: '版本号',
            dataIndex: 'version',
            key: 'version',
            width: 90,
            align: 'center'
        },
        {
            title: '操作',
            dataIndex: 'operate',
            key: 'operate',
            width: 120,
            fixed: 'right',
            align: 'center'
        },
    ])
    const data = ref([])
    const headers = ref({})
    const stepOptions = STEP_OPTIONS;
    const orgTreeData = ref([])
    const createdTime = ref(null)
    const searchForm = reactive({
        step: undefined,
        domainId: undefined,
        systemId: undefined,
        organizeId: undefined,
        username: undefined,
        startTime: undefined,
        endTime: undefined,
        name: undefined
    })
    const businessList = ref([])
    const systemList = ref([])
    const domainSystemList = ref([])

    // 获取文件列表
    const getTableList = async () => {
        loading.value = true
        try {
            if (createdTime.value) {
                searchForm.startTime = createdTime.value[0]
                searchForm.endTime = createdTime.value[1]
            }
            console.log('searchForm', searchForm);
            const params = {
                fileType: 0,
                pagingSort: {
                    currentPage: pagination.value.current,
                    pageSize: pagination.value.pageSize
                }, sortList: [], ...searchForm
            }
            fileApi.getFileList(params).then(res => {
                data.value = res.data.records
                pagination.value.total = res.data.total
            })
        } catch (error) {
            console.error('获取文件列表失败:', error)
            message.error('获取文件列表失败')
        } finally {
            loading.value = false
        }
    }

    // 下载文件
    const download = async (file) => {
        try {
            message.success(`开始下载文件：${file.name}`)
            // 使用 axios 进行文件下载，以便处理响应头
            const response = await axios.get(`/api/files/download/${file.id}`, {
                responseType: 'blob', // 设置响应类型为 blob
                headers: {
                    'Authorization': localStorage.getItem("token")
                }
            })

            // 创建 Blob URL
            const blob = new Blob([response.data], {
                type: response.headers['content-type']
            })
            const url = window.URL.createObjectURL(blob)

            // 创建一个隐藏的a标签来处理下载
            const link = document.createElement('a')
            link.href = url
            // 使用 encodeURIComponent 对文件名进行编码
            link.download = decodeURIComponent(file.name)
            document.body.appendChild(link)
            link.click()

            // 清理资源
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
        } catch (error) {
            console.error('下载文件错误:', error)
            message.error(`下载失败: ${error.message || '未知错误'}`)
        }
    }

    // 收藏/取消收藏文件
    const follow = async (record) => {
        if (!record || !record.id) {
            message.error('文件信息不完整')
            return
        }

        try {
            // 切换收藏状态 (1-收藏, 0-未收藏)
            const isImportant = record.isImportant ? 0 : 1
            const response = await fileApi.setImportant(record.id, isImportant)
            if (response.code == 200) {
                message.success('操作成功')
                getTableList()
            } else {
                message.error('操作失败')
            }
        } catch (error) {
            console.error('收藏操作失败:', error)
            message.error('操作失败')
        }
    }



    const filtersClick = (handler) => {
        switch (handler) {
            case 'search':
                getTableList()
                break
            case 'reset':
                searchForm.step = undefined; // 清空步骤
                searchForm.domainId = undefined; // 清空领域
                searchForm.systemId = undefined; // 清空系统
                searchForm.organizeId = undefined; // 清空组织
                searchForm.username = undefined; // 清空用户名
                searchForm.startTime = undefined; // 清空开始时间
                searchForm.endTime = undefined; // 清空开始时间
                searchForm.name = undefined; // 清空开始时间
                createdTime.value = null
                getTableList()
                break
        }
    }
    const { pagination, tableHeight, change, loading } = tableMixin({
        getTableList
    });
    const getOrganizeList = async () => {
        const res = await OrganizeApi.getOrgTree();
        console.log('organizeList', res);
        orgTreeData.value = res.data;
    }

    onMounted(async () => {
        getOrganizeList();
        const res = await bussinessApi.getBussinessSystems();
        console.log('systemList', res);
        domainSystemList.value = res.data;
        businessList.value = res.data.map(c => {
            return {
                label: c.name,
                value: c.id
            }
        })
    })
    const changeDomain = (e) => {
        // console.log('changeDomain', formState.value.domainId);
        searchForm.system = undefined;
        domainSystemList.value.forEach(c => {
            if (c.id == searchForm.domainId) {
                systemList.value = c.systems.map(c => {
                    return {
                        label: c.name,
                        value: c.id
                    }
                })
            }
        })
    }
</script>
<template>
    <div class="container">
        <breadcrumb></breadcrumb>
        <div class="page-content">
            <div class="page-filters">
                <!-- 第一行栅格：4个筛选项 -->
                <a-row :gutter="16">
                    <a-col :span="6">
                        <label class="filter-item"> <!-- 恢复水平排列 -->
                            <label class="filter-label">阶段：</label>
                            <a-select placeholder="请选择阶段" v-model:value="searchForm.step" style="flex: 1;"
                                :options="stepOptions"></a-select>
                            <!-- 输入框自适应宽度 -->
                        </label>
                    </a-col>

                    <a-col :span="6">
                        <label class="filter-item">
                            <label class="filter-label">分域：</label>
                            <a-select style="flex: 1;" placeholder="请选择分域" :options="businessList"
                                v-model:value="searchForm.domainId" @change="changeDomain"></a-select>
                        </label>
                    </a-col>

                    <a-col :span="6">
                        <label class="filter-item">
                            <label class="filter-label">系统：</label>
                            <a-select style="flex: 1;" placeholder="请选择系统" :options="systemList"
                                v-model:value="searchForm.systemId"></a-select>
                        </label>
                    </a-col>

                    <a-col :span="6">
                        <label class="filter-item">
                            <label class="filter-label">机构：</label>
                            <a-tree-select v-model:value="searchForm.organizeId" show-search style="width: 100%"
                                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择机构"
                                allow-clear tree-default-expand-all :tree-data="orgTreeData"
                                tree-node-filter-prop="label" :fieldNames="{ label: 'name', key: 'id', value: 'id' }">
                            </a-tree-select>
                            <!-- <a-select style="flex: 1;" placeholder="请选择机构"
                                v-model:value=""></a-select> -->
                        </label>
                    </a-col>
                </a-row>

                <!-- 第二行栅格：查询条件和按钮 -->
                <a-row :gutter="16" style="margin-top: 16px;">
                    <a-col :span="6">
                        <label class="filter-item">
                            <label class="filter-label">上传人：</label>
                            <a-input v-model:value="searchForm.username" placeholder="请输入" style="flex: 1;" />
                        </label>
                    </a-col>

                    <a-col :span="6">
                        <label class="filter-item">
                            <label class="filter-label">创建时间：</label>
                            <a-range-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="createdTime"
                                :placeholder="['开始日期', '结束日期']" style="flex: 1;" />
                        </label>
                    </a-col>

                    <a-col :span="6">
                        <label class="filter-item">
                            <label class="filter-label">文件名：</label>
                            <a-input class="user-input" v-model:value="searchForm.name" placeholder="请输入"
                                style="flex: 1;" />
                        </label>
                    </a-col>
                    <a-col :span="6">
                        <a-space :size="8"> <!-- 按钮右对齐 -->
                            <a-button type="primary" @click="filtersClick('search')">查询</a-button>
                            <a-button @click="filtersClick('reset')">重置</a-button>
                        </a-space>
                    </a-col>
                </a-row>
            </div>
            <ab-spin size="large" v-show="loading"></ab-spin>
            <div class="page-main">
                <a-table v-show="!loading" class="normal-table-component" :columns="columns"
                    :scroll="{ y: data.length === 0 ? 'auto' : tableHeight }" :data-source="data" :bodered="false"
                    :pagination="pagination" @change="change" :style="{ height: data.length === 0 ? '100%' : '100%' }">
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </template>
                        <template v-if="column.dataIndex === 'operate'">
                            <div class="operate operate-center">
                                <div class="operateIcon icon iconfont FCstar-fill"
                                    :style="{ color: record.isImportant === 1 ? '#F79009' : '' }"
                                    @click="follow(record)">
                                </div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateIcon icon iconfont FCdownload-2-fill" @click="download(record)">
                                </div>
                            </div>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
    </div>
    <importModel :visible="visible" @close="()=>{
        visible = false
    }"></importModel>
</template>
<style lang="less" scoped>
    @import url('./style.less');


    .container {
        padding: 16px;
    }

    .page-content {
        height: calc(100% - 44px);
        background-color: rgba(12, 17, 29, 0.7);
        padding: 16px;
        border: 1px solid @gray-9;
        border-radius: 2px;
    }

    .operateIcon {
        color: @gray-6;
        cursor: pointer;
    }

    .operate-center {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .page-filters {
        width: 100%;
        margin-bottom: 16px;
    }

    .filter-item {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
    }

    /* 统一标签样式（可选） */

    .page-filters .ant-space {
        width: 100%;

        label {
            width: auto;
            display: inline-block;
        }
    }

    /* 统一表单元素宽度（可选） */
    .page-filters .ant-select,
    .page-filters .ant-select-selector,
    .page-filters .ant-input,
    .page-filters .ant-picker {
        width: 100%;
    }

    /* 统一标签样式 */
    .filter-label {
        /* 设置标签最小宽度 */
        text-align: left;
        /* 标签文字右对齐 */
        font-weight: 500;
        white-space: nowrap;
        /* 防止标签文字换行 */
    }

    /* 统一表单元素样式 */
    .ant-select,
    .ant-input,
    .ant-picker {
        min-width: 0;
        /* 修复某些情况下flex无法收缩的问题 */
    }
</style>

<style lang="less">
    .ant-picker-dropdown .ant-picker-time-panel-column>li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
        color: @gray-5;
    }

    .ant-picker-dropdown .ant-picker-time-panel-column>li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
        background-color: @gray-10;
    }
</style>