<script setup>
import { ref, reactive, onMounted, computed, createVNode } from 'vue'
import { Modal, message } from 'ant-design-vue'
import {
    ExclamationCircleOutlined,
    UploadOutlined
} from '@ant-design/icons-vue'
import { tableMixin } from '@/mixins/table';
import { WEATHER_MAP, TEMPERATURE_MAP } from './common/style.map.ts'

import Dialog from './components/modal.vue'
import breadcrumb from '../home/<USER>/breadcrumb.vue';
import weatherApi from '@/api/weather/weather.api'
import axios from 'axios'
import WeatherApi from '@/api/weather/weather.api';
import { clone } from 'lodash-es';
const visible = ref(false)
const type = ref('add')
const filters = ref()
const search = reactive({
    dress: '',
    person: '',
    date: []
})
const current = ref({})
const headerConfig = ref({
    buttons: [
        {
            text: '新增数据',
            handler: 'add',
            icon: 'FCjiahao-1'
        }
    ],
    anvancedFilters: [
        {
            type: 'input',
            field: 'name',
            label: '观察场点',
            handler: 'search',
            placeholder: '请输入观察场点'
        },
        {
            type: 'input',
            field: 'name',
            label: '观察员',
            handler: 'search',
            placeholder: '请输入观察员'
        },
        {
            type: 'datepicker',
            field: 'name',
            label: '观察日期',
            handler: 'search',
            placeholder: '请输入选择'
        },
    ],
})
const columns = ref([
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        width: 60,
        fixed: 'left'
    },
    {
        title: '观察场位置',
        dataIndex: 'location',
        key: 'location',
        width: 120,
        ellipsis: true
    },
    {
        title: '观察日期',
        dataIndex: 'observationDate',
        key: 'observationDate',
        width: 120,
        ellipsis: true
    },
    {
        title: '天气现象',
        dataIndex: 'weatherPhenomenon',
        key: 'weatherPhenomenon',
        width: 100,
        ellipsis: true
    },
    {
        title: '温度/℃',
        dataIndex: 'temperature',
        key: 'temperature',
        width: 80,
        align: 'center'
    },
    {
        title: '湿度/%',
        dataIndex: 'humidity',
        key: 'humidity',
        width: 80,
        align: 'center'
    },
    {
        title: '风向',
        dataIndex: 'windDirection',
        key: 'windDirection',
        width: 80,
        align: 'center'
    },
    {
        title: '风速m/s',
        dataIndex: 'windSpeed',
        key: 'windSpeed',
        width: 90,
        align: 'center'
    },
    {
        title: '气压/hPa',
        dataIndex: 'pressure',
        key: 'pressure',
        width: 90,
        align: 'center'
    },
    {
        title: '观察员',
        dataIndex: 'observer',
        key: 'observer',
        width: 100,
        ellipsis: true
    },
    {
        title: '记录时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 160,
        ellipsis: true
    },
    {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 180,
        fixed: 'right',
        align: 'center'
    },
])

const data = ref([])

// todo 获取接口
const getTableList = async (search = {}) => {

    // 构造查询参数
    const params = {
        pagingSort:{
            currentPage:pagination.value.current,
            pageSize:pagination.value.pageSize
        },
        location: search.dress || '',
        startDate: search.date?.[0]? search.date?.[0] +' 00:00:00' : '',
        endDate: search.date?.[1]? search.date?.[1] +' 23:59:59' : '',
        observer: search.person || ''
    }

    // 2. 调用接口 赋值给 data.value
    const response = await weatherApi.getDataList(params)

    if (response.data) {
        // 更新表格数据
        data.value = response.data.records
        pagination.value.total = response.data.total

    } else {
        data.value = []
    }
}
//新增数据
const add = () => {
    type.value = 'add';
    visible.value = true;
}

//修改数据
const modify = (record) => {
    type.value = 'modify';
    current.value = clone(record);
    visible.value = true;
}

const check = (record) => {
    type.value = 'check';
    current.value = clone(record);
    visible.value = true;
}

//删除数据
const deleteRow = (record) => {
    const deleteOrg = (record) => {
        console.log('record', record);
        // todo 调用删除方法 调用成功后抛出成功标识，并刷新列表
        weatherApi.deleteData(record.id).then(res => {
            if (res.data) {
                message.success('删除成功');
                getTableList(search);
            }
        })
    }
    Modal.confirm({
        title: '提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: '是否确定删除所选项',
        onOk() {
            deleteOrg(record);
        },
        onCancel() {
        },
    });
}


//查询数据
const filtersClick = (handler) => {
    console.log(handler)
    switch (handler) {
        case 'add':
            add()
            break
        case 'search':
            getTableList(search)
            break
        case 'reset':
            search.dress = ''
            search.person = ''
            search.date = []
            getTableList()
            break
    }
}
const handleOk = () => {
    close();
    getTableList();

}
const close = () => {
    visible.value = false;
    current.value = {
        name: '',
        code: '',
        description: ''
    };
}
const getStyle = (weather) => {
    return WEATHER_MAP[weather]
}
const getTemperatureStyle = (temperature) => {
    if (temperature < 40) {
        return TEMPERATURE_MAP['low']
    } else if (temperature >= 40) {
        return TEMPERATURE_MAP['high']
    }

}
const { pagination, tableHeight, change, loading } = tableMixin({
    getTableList
});

</script>
<template>
    <div class="container">
        <breadcrumb></breadcrumb>
        <div class="page-content">
            <div class="page-filters">
                <a-button type="primary" @click="filtersClick('add')">新增数据</a-button>
                <a-space>
                    <label>观察场点：</label>
                    <a-input v-model:value="search.dress" placeholder="请输入"></a-input>
                    <label>观察员：</label>
                    <a-input class="user-input" v-model:value="search.person" placeholder="请输入"></a-input>
                    <label>观察日期：</label>
                    <a-range-picker valueFormat="YYYY-MM-DD" v-model:value="search.date"
                        :placeholder="['开始日期', '结束日期']" />
                    <a-button type="primary" @click="filtersClick('search')">查询</a-button>
                    <a-button @click="filtersClick('reset')">重置</a-button>
                </a-space>
            </div>
            <ab-spin size="large" v-show="loading"></ab-spin>
            <div class="page-main">
                <a-table v-show="!loading" class="normal-table-component" :columns="columns"
                    :scroll="{ y: data.length === 0 ? 'auto' : tableHeight }" :data-source="data" :bodered="false"
                    :pagination="pagination" @change="change"
                    :style="{ height: data.length === 0 ? tableHeight + 'px' : '100%' }">
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </template>
                        <template v-if="column.dataIndex === 'temperature'">
                            <span :style="getTemperatureStyle(record[column.dataIndex])">{{ record[column.dataIndex]
                            }}</span>
                        </template>
                        <template v-if="column.dataIndex === 'weather'">
                            <span class="weather-content" :style="getStyle(record[column.dataIndex])">{{
                                record[column.dataIndex] }}</span>
                        </template>
                        <template v-if="column.dataIndex === 'operate'">
                            <div class="operate">
                                <div class="operateBlue" @click="modify(record)">修改</div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateBlue" @click="check(record)">查看</div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateRed" @click="deleteRow(record)">删除</div>
                            </div>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
        <Dialog :visible="visible" :type="type" :row="current" @close="close" @handleOk="handleOk"></Dialog>
    </div>
</template>
<style lang="less" scoped>
@import url('./style.less');

.container {
    padding: 16px;
}

.page-content {
    height: calc(100% - 44px);
    background-color: rgba(12, 17, 29, 0.7);
    padding: 16px;
    border: 1px solid @gray-9;
    border-radius: 2px;
}

.operateIcon {
    color: @gray-6;
}

.user-input {
    width: 100px;
}

.page-filters {
    width: 100%;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
}

.page-main {
    height: calc(100% - 32px - 16px);
}

.greenColor {
    color: @green-5;
}

.weather-content {
    width: 64px;
    display: inline-block;
    text-align: center;
    border-radius: 3px;
    font-size: 14px;
}
</style>