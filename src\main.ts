import '@/assets/styles/index.less';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import abp from 'amber-design-pro';
import 'amber-design-pro/dist/style.css';
import 'amber-design-pro/dist/styles/military-theme.less';

import uploader from 'vue-simple-uploader'
import 'vue-simple-uploader/dist/style.css';
import './assets/styles/main.less';
import '@/assets/iconfont/iconfont.css';
import App from './App.vue'
import router from './router'
import { SvgIcon } from '@/components/svgIcon/index'

let app = null

app = createApp(App)
app.use(uploader)
app.use(router)
app.use(abp)
app.use(createPinia())
const app2: string | Element = '#app' // 避免 id 重复导致微应用挂载失败
app.component('SvgIcon', SvgIcon)
app.mount(app2)