<script setup>
import { reactive, defineEmits, defineProps, ref, onMounted, defineExpose, watch, onUnmounted } from "vue";
import { message } from 'ant-design-vue'
import SparkMD5 from 'spark-md5'
import { MD5Calculator } from '@/utils/md5Calculator'
import { cloneDeep } from 'lodash-es'
import { useUploadStore } from "@/stores/upload";
import emitter from "@/common/eventBus";
import indexedDBUtil from "@/utils/indexedDB";
import fileApi from "@/api/file/file.api";
import { getOptimizedConfig, formatFileSize, estimateUploadTime, getStepFromPath, validateStep, cleanupCompletedFile } from '@/config/uploadConfig';

const uploadStore = useUploadStore()
const props = defineProps({
    value: {
        type: Object,
        default: []
    },
    params: {
        type: Object,
        default: null
    },
})
const remainderStorageValue = ref(0)
const emits = defineEmits(['update:value'])
const fileList = ref([])

watch(() => props.value, (val) => {
    fileList.value = val
}, { deep: true })

onMounted(async () => {
    const cache = localStorage.getItem('storage')
    if (cache) {
        remainderStorageValue.value = JSON.parse(cache).storageSize
    }

    // 初始化 IndexedDB
    try {
        await indexedDBUtil.init();
        // 初始化完成后清理无效文件（不清理重复文件）
        await cleanupInvalidFiles();
    } catch (error) {
        console.error('Failed to initialize IndexedDB:', error);
    }

    // 监听浏览器关闭事件
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 监听页面可见性变化事件（备用方案）
    document.addEventListener('visibilitychange', handleVisibilityChange);
})

// 添加组件卸载时的清理
onUnmounted(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    
    // 取消MD5计算并清理资源
    if (md5Calculator.calculating) {
        md5Calculator.cancel();
    }
})

// 用于跟踪是否是真正的浏览器关闭
let isRealBrowserClose = false;

/**
 * 页面可见性变化处理函数（备用方案）
 * 注意：只在真正的浏览器关闭时才设置pausedByClose标记
 */
function handleVisibilityChange() {
    if (document.visibilityState === 'hidden') {
        // 延迟检查，如果是页面切换，通常会很快恢复可见
        setTimeout(() => {
            // 如果页面仍然隐藏且没有触发beforeunload，可能是浏览器最小化或切换应用
            // 这种情况下不设置pausedByClose标记
            if (document.visibilityState === 'hidden' && !isRealBrowserClose) {
                console.log('页面隐藏但非浏览器关闭，不设置pausedByClose标记');

                // 只暂停上传，不设置浏览器关闭标记
                const uploadingFiles = fileList.value.filter(file =>
                    !file.isComplete() && !file.error
                );

                if (uploadingFiles.length > 0) {
                    uploadingFiles.forEach(file => {
                        file.pause();
                    });
                    console.log('页面隐藏时暂停上传，但保持可恢复状态');
                }
            }
        }, 100);
    } else if (document.visibilityState === 'visible') {
        // 页面重新可见时，重置浏览器关闭标记
        isRealBrowserClose = false;
        console.log('页面重新可见，重置浏览器关闭标记');
    }
}

/**
 * 浏览器关闭前处理函数
 */
function handleBeforeUnload(event) {
    // 标记这是真正的浏览器关闭
    isRealBrowserClose = true;

    // 获取正在上传的文件
    const uploadingFiles = fileList.value.filter(file =>
        !file.isComplete() && !file.error
    );

    // 获取"执行中"列表的所有任务ID（taskStatus = 0）
    let inProgressTaskIds = [];
    try {
        // 方法1：从uploadStore中获取（最可靠）
        const storeTaskIds = uploadStore.state.inProgressTaskIds || [];
        if (storeTaskIds.length > 0) {
            inProgressTaskIds = storeTaskIds;
            console.log('🔴 从uploadStore获取到的进行中任务ID:', inProgressTaskIds);
        } else {
            // 方法2：从localStorage的任务映射获取
            const fileTaskMapping = localStorage.getItem('fileTaskMapping');
            if (fileTaskMapping) {
                const mapping = JSON.parse(fileTaskMapping);
                // 获取所有正在上传的文件对应的任务ID
                inProgressTaskIds = uploadingFiles
                    .map(file => mapping[file.uniqueIdentifier]?.id)
                    .filter(Boolean);
                console.log('🔴 从文件映射获取到的进行中任务ID:', inProgressTaskIds);
            }
        }

        if (inProgressTaskIds.length === 0) {
            console.warn('🔴 警告：未找到任何进行中的任务ID');
        }
    } catch (error) {
        console.error('🔴 获取进行中任务ID失败:', error);
        inProgressTaskIds = [];
    }

    if (uploadingFiles.length > 0 || inProgressTaskIds.length > 0) {
        // 先暂停所有正在上传的文件（立即生效）
        uploadingFiles.forEach(file => {
            file.pause();
        });

        // 立即调用暂停接口（在浏览器关闭前一刻）
        if (inProgressTaskIds.length > 0) {
            console.log('🔴 浏览器关闭前一刻，立即调用 setUploadStatus 接口，任务IDs:', inProgressTaskIds);

            // 使用 sendBeacon 发送请求（推荐用于页面卸载时）
            // 构造带有 token 的 URL
            const url = new URL(`${window.location.origin}/fileTask/setUploadStatus`);
            
            url.searchParams.append('token', localStorage.getItem("token") || '');

            // 使用 Blob 发送 JSON 数据
            const blob = new Blob([JSON.stringify(inProgressTaskIds)], {
                type: 'application/json'
            });

            const success = navigator.sendBeacon(url.toString(), blob);

            console.log('🔴 sendBeacon setUploadStatus 请求发送状态:', success ? '成功' : '失败', '任务ID:', inProgressTaskIds);

            // 如果 sendBeacon 失败，尝试同步 fetch
            if (!success) {
                try {
                    fetch(`${window.location.origin}/fileTask/setUploadStatus`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'token': localStorage.getItem("token") || ''
                        },
                        body: JSON.stringify(inProgressTaskIds),
                        keepalive: true // 保持连接直到请求完成
                    }).then(response => {
                        console.log('🔴 fetch setUploadStatus 请求响应状态:', response.status);
                    }).catch(error => {
                        console.error('🔴 fetch setUploadStatus 请求失败:', error);
                    });
                } catch (error) {
                    console.error('🔴 fetch 请求异常:', error);
                }
            }

            // 标记这些任务已经被暂停
            localStorage.setItem('pausedByClose', 'true');
            localStorage.setItem('pausedTaskIds', JSON.stringify(inProgressTaskIds));

            // 更新全局状态，清空进行中的任务ID
            uploadStore.updateInProgressTaskIds([]);
        }

        // 显示确认对话框
        event.preventDefault();
        event.returnValue = '有文件正在上传中，确定要离开吗？';
        return '有文件正在上传中，确定要离开吗？';
    }
}

const uploadState = reactive({
    uploadStatus: {},
    // 上传组件配置项（将在文件添加时动态更新）
    options: {
        target: `${window.location.origin}/filetransfer/uploadfile`, // 上传文件-目标 URL
        testMethod: 'POST',
        uploadMethod: 'POST',
        chunkSize: 32 * 1024 * 1024, // 默认分片大小32MB，将根据文件大小动态调整
        fileParameterName: 'file', //  上传文件时文件的参数名，默认 file
        maxChunkRetries: 2, // 默认重试次数，将根据文件大小动态调整
        simultaneousUploads: 6, // 默认并发数，将根据文件大小动态调整
        testChunks: true, //  是否开启分片已存在于服务器的校验
        // 服务器分片校验函数，秒传及断点续传基础
        checkChunkUploadedByResponse: function (chunk, message) {
            let objMessage = JSON.parse(message)
            if (objMessage.success) {
                let data = objMessage.data
                if (data.skipUpload) {
                    // 分片已存在于服务器中
                    return true
                }
                return (data.uploaded || []).indexOf(chunk.offset + 1) >= 0
            } else {
                return true
            }
        },
        headers: {
            token: localStorage.getItem("token")
        },
        query() { },
    },
    // 文件状态文案映射
    fileStatusText: {
        success: '上传成功',
        error: 'error',
        uploading: '上传中',
        paused: '暂停中',
        waiting: '等待中'
    },
    attrs: {
        accept: '*'
    },
    filesLength: 0,
})
const uploaderRef = ref(null)

// MD5计算器实例
const md5Calculator = new MD5Calculator()

/**
 * 清理IndexedDB中的无效文件数据（不清理重复文件，允许多次上传相同文件）
 */
async function cleanupInvalidFiles() {
    try {
        console.log('🔄 开始清理IndexedDB中的无效文件数据');

        // 获取所有存储的文件
        const allFiles = await indexedDBUtil.getAllFiles();
        if (!allFiles || allFiles.length === 0) {
            console.log('🔄 IndexedDB中没有文件数据，无需清理');
            return;
        }

        const invalidIdentifiers = [];

        // 检测无效文件（文件数据损坏或缺失）
        allFiles.forEach(fileData => {
            // 只清理明显无效的数据，不清理重复文件
            if (!fileData.file || !fileData.identifier || !fileData.totalChunks) {
                invalidIdentifiers.push(fileData.identifier);
                console.log(`🔄 发现无效文件数据，标识符: ${fileData.identifier}`);
            }
        });

        // 删除无效文件
        for (const identifier of invalidIdentifiers) {
            if (identifier) {
                await indexedDBUtil.deleteFile(identifier);
                console.log(`🔄 已删除无效文件数据: ${identifier}`);
            }
        }

        if (invalidIdentifiers.length > 0) {
            console.log(`🔄 清理完成，删除了 ${invalidIdentifiers.length} 个无效文件`);
        } else {
            console.log('🔄 没有发现无效文件，无需清理');
        }
    } catch (error) {
        console.error('🔄 清理无效文件时出错:', error);
    }
}

/**
 * 根据文件大小动态更新上传配置
 * @param file
 */
function updateUploadConfig(file) {
    const config = getOptimizedConfig(file.size);

    // 更新上传配置
    uploadState.options.chunkSize = config.chunkSize;
    uploadState.options.maxChunkRetries = config.maxChunkRetries;
    uploadState.options.simultaneousUploads = config.simultaneousUploads;

    console.log(`🔄 已为文件 ${file.name} (${formatFileSize(file.size)}) 应用优化配置:`);
    console.log(`🔄 分片大小: ${formatFileSize(config.chunkSize)}`);
    console.log(`🔄 并发数: ${config.simultaneousUploads}`);
    console.log(`🔄 重试次数: ${config.maxChunkRetries}`);
}

/**
 * 计算md5，实现断点续传及秒传
 * 使用优化的MD5Calculator，支持Web Worker和主线程两种模式
 * @param {object} file 文件信息
 */
async function computeMD5(file) {
    // 检查是否已经计算过MD5（仅在同一个文件对象实例中检查）
    const existingIdentifier = file.uniqueIdentifier;
    if (existingIdentifier && file._md5Computed) {
        console.log(`🔄 文件 ${file.name} 已有MD5标识符: ${existingIdentifier}，跳过重复计算`);
        // 直接使用已有的MD5进行后续处理
        calculateFileMD5End(existingIdentifier, file);
        return;
    }

    // 检查是否有其他文件正在计算MD5
    if (md5Calculator.calculating) {
        console.log(`🔄 有其他文件正在计算MD5，等待完成: ${md5Calculator.currentFileName}`);
        message.warning(`请等待文件 "${md5Calculator.currentFileName}" 的MD5计算完成`);
        return;
    }

    // 标记开始计算MD5
    file._md5Computing = true;

    // 根据文件大小获取优化配置
    const config = getOptimizedConfig(file.size);
    const chunkSize = config.md5ChunkSize;
    const totalChunks = Math.ceil(file.size / chunkSize);

    // 大文件MD5计算优化日志
    console.log(`🔄 开始计算MD5，文件: ${file.name}`);
    console.log(`🔄 文件大小: ${formatFileSize(file.size)}`);
    console.log(`🔄 MD5分片数: ${totalChunks}，分片大小: ${formatFileSize(chunkSize)}`);
    console.log(`🔄 预计耗时: ${estimateUploadTime(file.size, 50 * 1024 * 1024)} (基于50MB/s计算速度)`);
    
    // 判断是否使用Web Worker（大文件优先使用Worker）
    const useWebWorker = file.size > 100 * 1024 * 1024; // 100MB以上使用Worker
    if (useWebWorker) {
        console.log(`🔄 文件较大，使用Web Worker计算MD5: ${file.name}`);
    } else {
        console.log(`🔄 文件较小，使用主线程计算MD5: ${file.name}`);
    }

    // 文件状态设为"计算MD5"
    uploadState.uploadStatus[file.id] = '计算MD5 (0%)'
    file.pause()

    try {
        // 使用MD5Calculator计算MD5
        const result = await md5Calculator.calculateMD5({
            file: file.file,
            chunkSize,
            useWebWorker,
            onProgress: (progress, chunkIndex, totalChunks) => {
                // 更新进度显示
                uploadState.uploadStatus[file.id] = `计算MD5 ${progress}%`;
                
                // 大文件优化：每5%显示一次日志，减少日志输出
                if (progress % 5 === 0) {
                    console.log(`🔄 MD5计算进度: ${progress}% (${chunkIndex}/${totalChunks})`);
                }
                
                // 强制更新响应式状态
                uploadState.uploadStatus = JSON.parse(JSON.stringify(uploadState.uploadStatus));
            },
            onComplete: (md5, duration) => {
                console.log(`✅ MD5计算完成，耗时: ${duration.toFixed(2)}秒，文件大小: ${(file.size / 1024 / 1024 / 1024).toFixed(2)}GB，MD5: ${md5}`);
            },
            onError: (error) => {
                console.error(`❌ MD5计算失败: ${file.name}`, error);
                message.error(`文件 ${file.name} MD5计算失败: ${error}`);
            }
        });

        // 标记MD5计算完成
        file._md5Computing = false;
        file._md5Computed = true;

        // 处理计算结果
        calculateFileMD5End(result.md5, file);

    } catch (error) {
        console.error(`❌ MD5计算异常: ${file.name}`, error);
        
        // 重置状态
        file._md5Computing = false;
        uploadState.uploadStatus[file.id] = 'MD5计算失败';
        
        // 显示错误信息
        if (error.message.includes('已取消')) {
            message.warning(`文件 ${file.name} MD5计算已取消`);
        } else {
            message.error(`文件 ${file.name} MD5计算失败: ${error.message}`);
        }
        
        // 取消文件上传
        file.cancel();
    }
}

/**
   * 文件MD5计算结束
   * @param {string} md5 文件 MD5 值
   * @param {object} file 文件对象
   */
async function calculateFileMD5End(md5, file) {
    console.log(`🔄 MD5计算完成，开始处理文件: ${file.name}，MD5: ${md5}`);
    console.log(`🔄 文件identifier: ${file.uniqueIdentifier}`);

    // 🔥 重要：不要修改file.uniqueIdentifier，保持vue-simple-uploader生成的值
    // MD5仅用于后端API调用，identifier用于前端状态管理

    // 🔥 重要：检查是否已存在相同文件的未完成任务（使用MD5检查，因为后端存储的是MD5）
    try {
        // 先检查后端是否有相同MD5的未完成任务
        const currentBatchId = localStorage.getItem('currentBatchId');
        if (currentBatchId) {
            // 查询进行中和暂停状态的任务
            const checkTasks = async (taskStatus) => {
                const params = {
                    pagingSort: {
                        currentPage: 1,
                        pageSize: 1000
                    },
                    sortList: [],
                    taskStatus: taskStatus
                };
                
                try {
                    const response = await fileApi.listByBatchId(params);
                    return response.data.resultPage.records;
                } catch (error) {
                    console.error(`查询任务状态 ${taskStatus} 失败:`, error);
                    return [];
                }
            };
            
            // 检查进行中的任务 (status = 0)，使用MD5比较
            const inProgressTasks = await checkTasks(0);
            let existingTask = inProgressTasks.find(task => task.identifier === md5);
            
            if (existingTask) {
                console.log(`🔄 发现相同文件的进行中任务: ${existingTask.fileName}, 任务ID: ${existingTask.id}`);
                message.warning(`文件 "${file.name}" 正在上传中，请勿重复选择`);
                file.cancel(); // 取消当前文件的上传准备
                return;
            }
            
            // 检查暂停状态的任务 (status = 2)，使用MD5比较
            const pausedTasks = await checkTasks(2);
            existingTask = pausedTasks.find(task => task.identifier === md5);
            
            if (existingTask) {
                console.log(`🔄 发现相同文件的暂停任务: ${existingTask.fileName}, 任务ID: ${existingTask.id}`);
                message.warning({
                    content: `文件 "${file.name}" 有未完成的暂停任务，请在任务列表中点击"继续"来恢复上传`,
                    duration: 5 // 延长显示时间到5秒
                });
                file.cancel(); // 取消当前文件的上传准备
                return;
            }
        }
    } catch (error) {
        console.error('检查未完成任务时出错:', error);
        
        // 🔥 重要：如果API检查失败，使用本地存储进行二次检查
        try {
            // 检查本地的文件任务映射（使用MD5作为key）
            const fileTaskMapping = localStorage.getItem('fileTaskMapping');
            if (fileTaskMapping) {
                const mappingObj = JSON.parse(fileTaskMapping);
                const existingTaskId = mappingObj[md5];
                
                if (existingTaskId) {
                    console.log(`🔄 通过本地映射发现相同文件的任务ID: ${existingTaskId}`);
                    message.warning({
                        content: `文件 "${file.name}" 可能有未完成的任务，请在任务列表中检查`,
                        duration: 5
                    });
                    file.cancel(); // 取消当前文件的上传准备
                    return;
                }
            }
            
            // 检查IndexedDB中是否有相同文件（使用MD5作为key）
            const existingFile = await indexedDBUtil.getFile(md5);
            if (existingFile) {
                console.log(`🔄 通过IndexedDB发现相同文件，可能有未完成任务`);
                message.warning({
                    content: `检测到相同文件 "${file.name}"，建议先检查任务列表确认无重复任务`,
                    duration: 5
                });
                // 注意：这里不直接return，而是继续执行，但给用户警告
            }
        } catch (localCheckError) {
            console.error('本地检查也失败:', localCheckError);
            // 最后的安全措施：如果所有检查都失败，阻止创建新任务
            message.error('无法验证文件状态，请刷新页面后重试');
            file.cancel();
            return;
        }
    }

    // 🔥 注意：不再自动清理IndexedDB中的相同文件，因为可能对应暂停的任务
    // 上面的检查逻辑已经处理了重复任务的情况
    
    // 获取当前页面的step参数
    const currentStep = getStepFromPath();
    console.log(`🔄 当前页面step参数: ${currentStep}`);

    // 验证step参数
    if (!validateStep(currentStep)) {
        console.error(`🔄 无效的step参数: ${currentStep}`);
    }

    // 将自定义参数直接加载uploader实例的opts上，包含step参数
    Object.assign(uploaderRef.value.uploader.opts, {
        query: {
            ...props.params,
            step: currentStep // 添加step参数
        }
    })
    
    // 🔥 重要：不修改file.uniqueIdentifier，保持vue-simple-uploader的原始值
    // 但我们需要建立identifier和MD5的映射关系
    const fileIdentifier = file.uniqueIdentifier;
    
    console.log(`🔄 准备创建新文件任务: ${file.name}, vue-uploader-id: ${fileIdentifier}, MD5: ${md5}`);

    // 🔥 最后安全检查：确保没有在本地映射中找到相同的任务（使用MD5检查）
    try {
        const fileTaskMapping = localStorage.getItem('fileTaskMapping');
        if (fileTaskMapping) {
            const mappingObj = JSON.parse(fileTaskMapping);
            if (mappingObj[md5]) {
                console.warn(`⚠️ 在最后检查中发现相同文件的任务映射，停止创建新任务: ${md5}`);
                // message.warning(`文件 "${file.name}" 可能已有任务记录，请检查任务列表`);
                file.cancel();
                return;
            }
        }
    } catch (error) {
        console.error('最后安全检查失败:', error);
    }

    // 计算总分片数
    const totalChunks = Math.ceil(file.size / uploadState.options.chunkSize);

    // 将文件信息存储到 IndexedDB（使用MD5作为key，但保存vue-uploader的identifier）
    try {
        await indexedDBUtil.saveFile({
            file: file.file,
            identifier: md5, // IndexedDB使用MD5作为key，便于后续查找
            vueUploaderIdentifier: fileIdentifier, // 保存vue-uploader的identifier
            totalChunks: totalChunks
        });
        console.log(`🔄 新文件 ${file.name} 信息已保存到 IndexedDB，MD5: ${md5}, vue-uploader-id: ${fileIdentifier}`);
    } catch (error) {
        console.error('🔄 保存文件到 IndexedDB 失败:', error);
    }

    // 通知任务管理器文件开始上传（使用vue-uploader的identifier）
    emitter.emit('file.upload.started', { identifier: fileIdentifier });
    console.log(`🔄 通知新文件开始上传: ${fileIdentifier}`);

    // file.resume()
    // 移除自定义状态
    uploadState.uploadStatus[file.id] = ''
    uploadState.uploadStatus = JSON.parse(JSON.stringify(uploadState.uploadStatus))
}

/**
  * 添加批量文件的回调函数
  * @description 对单个或批量文件都按此逻辑处理
  * @param {object} files 批量文件信息
  */
function handleFilesAdded(files) {
    // 添加调试日志，查看文件信息
    console.log('📂 文件添加事件触发，文件数量:', files.length);
    
    // 检查是否有文件夹上传（通过relativePath判断）
    const hasFolders = files.some(file => file.relativePath && file.relativePath !== file.name);
    console.log('📂 是否包含文件夹上传:', hasFolders);
    
    if (hasFolders) {
        // 如果是文件夹上传，打印每个文件的relativePath
        console.log('📂 文件夹上传详情:');
        const folderStructure = {};
        
        files.forEach(file => {
            console.log(`📂 文件: ${file.name}, relativePath: ${file.relativePath}`);
            
            // 提取文件夹名称（第一级目录）
            if (file.relativePath) {
                const folderName = file.relativePath.split('/')[0];
                if (!folderStructure[folderName]) {
                    folderStructure[folderName] = [];
                }
                folderStructure[folderName].push(file.relativePath);
            }
        });
        
        console.log('📂 文件夹结构:', folderStructure);
    }
    
    fileList.value = files
    emits('update:value', files)
    // 批量选择的文件的总体大小
    const filesTotalSize = files
        .map((item) => item.size)
        .reduce((pre, next) => {
            return pre + next
        }, 0)
    if (remainderStorageValue < filesTotalSize) {
        // 批量选择的文件超出剩余存储空间
        message.warning(
            `剩余存储空间不足，请重新选择${files.length > 1 ? '批量' : ''}文件`
        )
        files.ignored = true //  本次选择的文件过滤掉
    } else {
        // 批量或单个选择的文件未超出剩余存储空间，正常上传
        uploadState.filesLength += files.length

        // 文件去重处理（仅在当前批次中去重，允许多次上传相同文件）
        const uniqueFiles = [];
        const currentBatchFileMap = new Map(); // 用于检测当前批次中的重复文件

        files.forEach((file) => {
            // 生成当前批次的文件唯一标识（基于文件名、大小、最后修改时间）
            const fileKey = `${file.name}_${file.size}_${file.file.lastModified}`;

            if (currentBatchFileMap.has(fileKey)) {
                console.log(`🔄 检测到当前批次中的重复文件，跳过: ${file.name}`);
                message.warning(`文件 ${file.name} 在当前选择中重复，跳过重复添加`);
                return;
            }

            currentBatchFileMap.set(fileKey, file);
            uniqueFiles.push(file);
        });

        console.log(`🔄 当前批次文件去重完成，原始文件数: ${files.length}，去重后文件数: ${uniqueFiles.length}`);

        // 如果有文件被添加，给用户友好提示
        if (uniqueFiles.length > 0) {
            console.log(`🔄 开始处理 ${uniqueFiles.length} 个文件`);
            if (uniqueFiles.length === 1) {
                message.info(`开始处理文件: ${uniqueFiles[0].name}`);
            } else {
                message.info(`开始处理 ${uniqueFiles.length} 个文件`);
            }
        }

        uniqueFiles.forEach((file) => {
            file.pause()
            // 根据文件大小动态更新上传配置
            updateUploadConfig(file)
            computeMD5(file)
        })
    }
}

/**
   * 文件上传成功 回调函数
   * @param {object} rootFile 成功上传的文件所属的根 Uploader.File 对象，它应该包含或者等于成功上传文件
   * @param {object} file 当前成功的 Uploader.File 对象本身
   * @param {string} response 服务端响应内容，永远都是字符串
   */
async function handleFileSuccess(rootFile, file, response) {
    if (response == '') {
        uploadState.uploadStatus[file.id] = '上传失败'
        // this.callback(false)
        return
    }

    let result = JSON.parse(response)
    if (result.success) {
        file.statusStr = ''

        if (file.uniqueIdentifier) {
            console.log(`🎯 文件上传成功，开始处理完成逻辑: ${file.uniqueIdentifier}`);

            // 🔥 步骤1：立即标记为已完成状态，防止任何进度回退
            markFileAsCompleted(file.uniqueIdentifier);
            console.log(`✅ 文件已标记为完成状态: ${file.uniqueIdentifier}`);

            // 🔥 步骤2：立即设置进度为100%并锁定
            uploadStore.updateProgress(file.uniqueIdentifier, 1);
            console.log(`✅ 进度已锁定为100%: ${file.uniqueIdentifier}`);

            // 🔥 步骤3：通知任务管理器文件上传完成
            emitter.emit('file.upload.completed', { identifier: file.uniqueIdentifier });
            console.log(`✅ 已通知任务管理器文件完成: ${file.uniqueIdentifier}`);

            // 🔥 步骤4：调用成功状态接口（但不立即刷新任务列表）
            try {
                await markFileTaskAsSuccess(file.uniqueIdentifier);
                console.log(`✅ 任务状态已设置为成功: ${file.uniqueIdentifier}`);
            } catch (error) {
                console.error(`❌ 设置任务成功状态失败: ${file.uniqueIdentifier}`, error);
            }

            // 🔥 步骤5：延迟清理，确保UI显示100%足够长时间
            setTimeout(async () => {
                try {
                    // 清理IndexedDB数据
                    await indexedDBUtil.deleteFile(file.uniqueIdentifier);
                    console.log(`✅ IndexedDB数据已清理: ${file.uniqueIdentifier}`);

                    // 使用统一清理函数清除其他相关数据
                    await cleanupCompletedFile(file.uniqueIdentifier);
                    console.log(`✅ 所有相关数据已清理: ${file.uniqueIdentifier}`);
                } catch (error) {
                    console.error(`❌ 清理数据失败: ${file.uniqueIdentifier}`, error);
                }
            }, 2000); // 延长到2秒，确保用户能看到100%状态
        }

        if (uploadState.filesLength === 1) {
            // 本次所有的文件均已上传完毕
            message.success(`上传完毕`)
            // this.callback(true)
        }
    } else {
        uploadState.uploadStatus[file.id] = '上传失败'
    }
    uploadState.filesLength--
}

/**
 * 文件上传失败 回调函数
 * @param {object} rootFile 成功上传的文件所属的根 Uploader.File 对象，它应该包含或者等于成功上传文件
 * @param {object} file 当前成功的 Uploader.File 对象本身
 * @param {string} response 服务端响应内容，永远都是字符串
 */
async function handleFileError(rootFile, file, response) {
    message.error(response)

    // 文件上传失败时，保留 IndexedDB 中的文件信息以便后续重试
    console.log(`文件 ${file.uniqueIdentifier} 上传失败，保留 IndexedDB 信息以便重试`)
}

/**
 * 取消上传
 * @param uniqueIdentifier 文件唯一标识
 */
async function cancelUpload(uniqueIdentifier) {
    const { uploader } = uploaderRef.value
    if (uniqueIdentifier) {
        const targetFile = uploader.files.find(file => file.uniqueIdentifier === uniqueIdentifier);
        targetFile.cancel()
        const list = fileList.value.filter(item => item.uniqueIdentifier !== uniqueIdentifier)
        fileList.value = list
        emits('update:value', list)

        // 通知任务管理器文件已取消
        emitter.emit('file.upload.completed', { identifier: uniqueIdentifier });

        // 清理进度并从 IndexedDB 中删除该文件信息
        try {
            // 清理该文件的上传进度
            uploadStore.clearProgress(uniqueIdentifier);
            
            await indexedDBUtil.deleteFile(uniqueIdentifier)
            console.log(`取消上传：文件 ${uniqueIdentifier} 已从 IndexedDB 中删除`)
        } catch (error) {
            console.error('删除 IndexedDB 文件失败:', error)
        }
    } else {
        fileList.value = []
        uploader.cancel()
        emits('update:value', [])

        // 清空所有进度和 IndexedDB 文件信息
        try {
            // 清空所有上传进度
            uploadStore.clearAllProgress();
            
            await indexedDBUtil.clearAll()
            console.log('所有文件已从 IndexedDB 中清除')
        } catch (error) {
            console.error('清空 IndexedDB 失败:', error)
        }
    }

}

/**
 * 暂停
 * @param uniqueIdentifier 文件唯一标识
 * 
 */
function pauseUpload(uniqueIdentifier) {
    const { uploader } = uploaderRef.value
    const targetFile = uploader.files.find(file => file.uniqueIdentifier === uniqueIdentifier);
    if (targetFile) {
        targetFile.pause(); // 暂停上传
        // 暂停时，移除进度更新源控制，让原生组件继续监控进度
        progressUpdateSources.value.delete(uniqueIdentifier);
        // 通知任务管理器文件已暂停
        emitter.emit('file.upload.paused', { identifier: uniqueIdentifier });
        console.log(`${uniqueIdentifier} 已暂停`);
    }
}

/**
 * 开始、继续上传
 * @param uniqueIdentifier 文件唯一标识
 * 
 */
async function resumeUpload(uniqueIdentifier) {
    const { uploader } = uploaderRef.value
    if (uniqueIdentifier) {
        let targetFile = uploader.files.find(file => file.uniqueIdentifier === uniqueIdentifier);

        // 如果在当前上传队列中找不到文件，尝试从 IndexedDB 恢复
        if (!targetFile) {
            try {
                const fileData = await indexedDBUtil.getFile(uniqueIdentifier);
                if (fileData) {
                    console.log(`从 IndexedDB 恢复文件: ${uniqueIdentifier}`);

                    // 将文件重新添加到上传队列
                    uploader.addFile(fileData.file);

                    // 等待文件被添加到队列后再查找
                    setTimeout(() => {
                        targetFile = uploader.files.find(file => file.uniqueIdentifier === uniqueIdentifier);
                        if (targetFile) {
                            targetFile.uniqueIdentifier = uniqueIdentifier;
                            targetFile.resume();
                            // 通知任务管理器文件开始上传
                            emitter.emit('file.upload.started', { identifier: uniqueIdentifier });
                            console.log(`${uniqueIdentifier} 从 IndexedDB 恢复并继续上传`);
                        }
                    }, 100);
                } else {
                    console.warn(`文件 ${uniqueIdentifier} 在 IndexedDB 中不存在`);
                }
            } catch (error) {
                console.error(`从 IndexedDB 恢复文件失败:`, error);
            }
        } else {
            targetFile.resume(); // 继续上传
            // 通知任务管理器文件开始上传
            emitter.emit('file.upload.started', { identifier: uniqueIdentifier });
            console.log(`${uniqueIdentifier} 继续上传`);
        }
    } else {
        Object.assign(uploaderRef.value.uploader.opts, {
            query: {
                ...props.params
            }
        })
        uploader.resume()
    }

    // 只有当文件不被手动上传器控制时，才启动进度监控
    if (uniqueIdentifier && !progressUpdateSources.value.has(uniqueIdentifier)) {
        getProgress()
    } else if (!uniqueIdentifier) {
        getProgress()
    }
}

/**
 * 标记文件任务为成功状态
 * @param identifier 文件标识符
 */
async function markFileTaskAsSuccess(identifier) {
    try {
        console.log(`🎯 开始设置任务成功状态: ${identifier}`);

        // 首先尝试从localStorage中的映射关系获取任务ID
        const fileTaskMapping = localStorage.getItem('fileTaskMapping');
        let taskId = null;

        if (fileTaskMapping) {
            const mappingObj = JSON.parse(fileTaskMapping);
            taskId = mappingObj[identifier];
        }

        // 如果从映射中找到了任务ID，直接使用
        if (taskId) {
            console.log(`✅ 从映射中找到任务ID: ${taskId}，调用批量设置成功状态接口`);
            await fileApi.batchSuccess([taskId]);
            console.log(`✅ 任务 ${taskId} 状态已设置为成功`);

            // 将任务ID添加到已完成列表，防止后续调用其他状态更新接口
            addToCompletedTasks(taskId);

            // 从映射中移除已完成的任务
            const mappingObj = JSON.parse(fileTaskMapping);
            delete mappingObj[identifier];
            localStorage.setItem('fileTaskMapping', JSON.stringify(mappingObj));

            // 🔥 重要：不在这里立即刷新任务列表，避免进度回退
            // 任务列表会通过其他机制自动刷新
            console.log(`✅ 任务状态设置完成，避免立即刷新任务列表: ${taskId}`);
            return;
        }
        
        // 如果映射中没有找到，尝试通过API查询
        console.warn(`映射中未找到文件 ${identifier} 的任务ID，尝试通过API查询`);
        
        // 获取当前批次ID
        const currentBatchId = localStorage.getItem('currentBatchId');
        if (!currentBatchId) {
            console.warn('未找到当前批次ID，跳过状态设置');
            return;
        }

        // 通过批次ID获取任务列表，找到对应的任务ID
        const params = {
            pagingSort: {
                currentPage: 1,
                pageSize: 1000 // 设置较大的页面大小以获取所有任务
            },
            sortList: [],
            taskStatus: 0 // 进行中的任务
        };

        const res = await fileApi.listByBatchId(params);
        const tasks = res.data.resultPage.records;
        
        // 根据文件标识符找到对应的任务
        const task = tasks.find(t => t.identifier === identifier);
        if (task && task.id) {
            console.log(`通过API查询找到任务ID: ${task.id}，调用批量设置成功状态接口`);
            await fileApi.batchSuccess([task.id]);
            console.log(`任务 ${task.id} 状态已设置为成功`);
            
            // 将任务ID添加到已完成列表，防止后续调用其他状态更新接口
            addToCompletedTasks(task.id);
        } else {
            console.warn(`未找到文件 ${identifier} 对应的任务ID`);
        }
    } catch (error) {
        console.error('设置任务成功状态失败:', error);
        // 不抛出错误，避免影响上传流程
    }
}

/**
 * 将任务ID添加到已完成列表
 * @param taskId 任务ID
 */
function addToCompletedTasks(taskId) {
    try {
        const completedTasks = getCompletedTasks();
        if (!completedTasks.includes(taskId)) {
            completedTasks.push(taskId);
            localStorage.setItem('completedTaskIds', JSON.stringify(completedTasks));
            console.log(`任务 ${taskId} 已添加到已完成列表`);
        }
    } catch (error) {
        console.error('添加到已完成列表失败:', error);
    }
}

/**
 * 获取已完成的任务ID列表
 */
function getCompletedTasks() {
    try {
        const completedTasks = localStorage.getItem('completedTaskIds');
        return completedTasks ? JSON.parse(completedTasks) : [];
    } catch (error) {
        console.error('获取已完成任务列表失败:', error);
        return [];
    }
}

/**
 * 标记文件为已完成状态，防止进度被重置
 */
function markFileAsCompleted(identifier) {
    try {
        const completedFiles = getCompletedFiles();
        if (!completedFiles.includes(identifier)) {
            completedFiles.push(identifier);
            localStorage.setItem('completedFileIdentifiers', JSON.stringify(completedFiles));
            console.log(`文件 ${identifier} 已标记为完成状态，防止进度重置`);
        }
    } catch (error) {
        console.error('标记文件完成状态失败:', error);
    }
}

/**
 * 获取已完成的文件标识符列表
 */
function getCompletedFiles() {
    try {
        const completedFiles = localStorage.getItem('completedFileIdentifiers');
        return completedFiles ? JSON.parse(completedFiles) : [];
    } catch (error) {
        console.error('获取已完成文件列表失败:', error);
        return [];
    }
}

// 添加进度更新控制标记
const progressUpdateSources = ref(new Set()) // 跟踪哪些文件正在被其他源更新进度
const isProgressMonitoring = ref(false) // 防止多个 getProgress 同时运行

function getProgress() {
    // 防止多个进度监控同时运行
    if (isProgressMonitoring.value) {
        return;
    }
    isProgressMonitoring.value = true;
    const { uploader } = uploaderRef.value
    const progressMap = {}
    let isAllCompleted = true
    
    // 获取当前存储的进度
    const currentProgress = uploadStore.state.progress;
    
    for (const file of uploader.files) {
        // 🔥 重要：检查文件是否已完成，如果已完成则跳过进度更新
        const completedFiles = getCompletedFiles();
        if (completedFiles.includes(file.uniqueIdentifier)) {
            console.log(`🛡️ 文件 ${file.uniqueIdentifier} 已完成，跳过进度更新，保持100%`);
            // 确保已完成文件的进度保持100%
            if (currentProgress[file.uniqueIdentifier] !== 1) {
                progressMap[file.uniqueIdentifier] = 1;
                console.log(`🛡️ 强制设置已完成文件进度为100%: ${file.uniqueIdentifier}`);
            }
            continue;
        }

        // 只有当文件没有被其他进度源控制时，才使用原生上传组件的进度
        if (!progressUpdateSources.value.has(file.uniqueIdentifier)) {
            const fileProgress = file.progress();
            const storedProgress = currentProgress[file.uniqueIdentifier] || 0;

            // 只有当新进度大于或等于当前存储的进度时才更新，防止进度回退
            if (fileProgress >= storedProgress) {
                progressMap[file.uniqueIdentifier] = fileProgress;
                
                // 🔥 重要：如果进度达到100%，立即标记为已完成
                if (fileProgress >= 1) {
                    markFileAsCompleted(file.uniqueIdentifier);
                    console.log(`🛡️ 原生上传器文件 ${file.uniqueIdentifier} 达到100%，已标记为完成`);
                }
                
                // 只在进度有显著变化时记录日志
                if (Math.abs(fileProgress - storedProgress) > 0.01 || fileProgress === 1) {
                    console.log(`📈 原生上传器进度更新: ${file.uniqueIdentifier}, 进度: ${(fileProgress * 100).toFixed(2)}%, 存储进度: ${(storedProgress * 100).toFixed(2)}%`);
                }
            } else {
                // 只在进度差异较大时记录警告
                if (storedProgress - fileProgress > 0.05) {
                    console.warn(`⚠️ 跳过原生进度更新，文件: ${file.uniqueIdentifier}, 新进度(${(fileProgress * 100).toFixed(2)}%)小于存储进度(${(storedProgress * 100).toFixed(2)}%)`);
                }
            }
        }

        if (file.progress() !== 1) {
            isAllCompleted = false
        }
    }
    
    // 只更新不被其他源控制的文件进度，且只有当有进度需要更新时才调用
    if (Object.keys(progressMap).length > 0) {
        uploadStore.updateProgress(progressMap)
    }
    
    if (!isAllCompleted) {
        setTimeout(() => {
            isProgressMonitoring.value = false;
            getProgress()
        }, [1000])
    } else {
        isProgressMonitoring.value = false;
    }
}

function onHandlePause({ uniqueIdentifiers }) {
    for (const uniqueIdentifier of uniqueIdentifiers) {
        pauseUpload(uniqueIdentifier)
    }
}

function onHandleResume({ uniqueIdentifiers }) {
    for (const uniqueIdentifier of uniqueIdentifiers) {
        resumeUpload(uniqueIdentifier)
    }
}

emitter.on('pause.upload', onHandlePause)
emitter.on('resume.upload', onHandleResume)

// 监听进度源标记事件
function onMarkProgressSource({ identifier, isManual, currentProgress }) {
    if (isManual) {
        // 标记这个文件的进度由手动上传器控制
        progressUpdateSources.value.add(identifier);
        console.log(`文件 ${identifier} 进度控制权转移到手动上传器，当前进度: ${currentProgress ? (currentProgress * 100).toFixed(2) + '%' : '未知'}`);
        
        // 如果提供了当前进度，确保手动上传器从这个进度开始，避免进度回退
        if (currentProgress && currentProgress > 0) {
            // 确保进度存储中有这个值，防止后续被覆盖
            uploadStore.updateProgress(identifier, currentProgress);
        }
    } else {
        // 移除标记，恢复原生上传组件的进度控制
        progressUpdateSources.value.delete(identifier);
        console.log(`文件 ${identifier} 进度控制权恢复到原生上传组件`);
        
        // 恢复进度监控
        getProgress();
    }
}
emitter.on('mark.progress.source', onMarkProgressSource)

watch(() => uploadStore.state.cancelList, (val) => {
    if (val.length > 0) {
        for (const uniqueIdentifier of val) {
            cancelUpload(uniqueIdentifier)
        }
    }
}, { deep: true })

defineExpose({
    cancelUpload,
    pauseUpload,
    resumeUpload,
    getProgress,
});

onUnmounted(() => {
    emitter.off('pause.upload', onHandlePause)
    emitter.off('resume.upload', onHandleResume)
    emitter.off('mark.progress.source', onMarkProgressSource)
});
</script>

<template>
    <uploader ref="uploaderRef" :options="uploadState.options" :auto-start="false" class="uploader-container"
        @files-added="handleFilesAdded" @file-success="handleFileSuccess">
        <uploader-unsupport>您的浏览器不支持上传组件</uploader-unsupport>
        <a-flex :gap="16">
            <uploader-btn class="select-btn" :attrs="uploadState.attrs">选择文件</uploader-btn>
            <uploader-btn class="select-btn" :attrs="uploadState.attrs" :directory="true">选择文件夹</uploader-btn>
        </a-flex>
        <uploader-list>
            <template v-if="fileList.length > 0">
                <a-tag v-for="file in fileList" :key="file.uniqueIdentifier" closable
                    @close="() => cancelUpload(file.uniqueIdentifier)" color="green">
                    {{ file.name }}
                </a-tag>
            </template>
            <span v-else> </span>
        </uploader-list>
    </uploader>
</template>


<style lang="less">
.uploader-container {
    .select-btn {
        border-color: #2970FF;
        color: #2970FF;
    }

    .uploader-list {
        padding: 8px 0;
        max-height: 100px;
        overflow: auto;
    }
}
</style>