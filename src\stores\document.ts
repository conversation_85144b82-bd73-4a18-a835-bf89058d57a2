import { ref, computed, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useDocumentStore = defineStore('documentState', () => {

  const documentState: any = reactive({
    curSelectFolder: undefined, // 活动选择
    curSelectData: undefined, // 数据选择
  })

  function updateSelectFolder(folder: any) {
    documentState.curSelectFoldr = folder
  }

  function updateSelectData(data: any) {
    documentState.curSelectData = data
  }

  return { documentState, updateSelectFolder, updateSelectData }
})

