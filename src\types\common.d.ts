import { AxiosPromise, AxiosResponse } from "axios";

export interface Pager {
 currentPage?: number;
 pageSize?: number;
 totalCount?: number;
}

export interface SearchForm {
 name?: string;
 category?: string;
}

export interface AxiosReturnTypes extends AxiosResponse {
 code: string;
 data: any;
 result: any;
 message: string;
 requestId: string;
 timestamp: number;
 uri: string;
 totalCount:number;
 [keys: string]: any;
}

export interface PagerTypes {
 currentPage: number;
 offset: number;
 pageCount: number;
 pageSize: number;
 totalCount: number;
}


interface AxiosListTypes extends AxiosReturnTypes{
  data:{
    list: [],
    page: Pager
  }
}