<script setup>
import { ref, reactive, defineProps, onMounted, onUnmounted, watch, createVNode, defineEmits, nextTick } from 'vue';
import fileApi from '@/api/file/file.api'
import { MoreOutlined, PlusOutlined, EditOutlined, DeleteOutlined, DownloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import abTree from '@/components/tree/index.vue';
import { Modal, message } from 'ant-design-vue'
import emitter from '@/common/eventBus';


const emits = defineEmits(['change'])
// 树
const treeState = reactive({
    fieldNames: {
        children: 'children',
        title: 'fileName',
        key: 'userFileId',
    },
    expandedKeys: [],
    selectedKeys: [],
    list: [],
    currentNode: {}
})

// 弹窗
const modalState = reactive({
    visible: false,
    title: '新建文件夹',
    type: 'add',
    detail: undefined
})

const formState = ref({
    fileName: undefined,
    filePath: '/'
})

/**
   * 加载树目录数据
   * @param treeNode 
   */
const onLoadTreeData = (treeNode) => {
    return new Promise(async resolve => {
        if (treeNode.dataRef.children) {
            resolve();
            return;
        }
        if (!treeNode.dataRef.children) {
            let filePath = treeNode.dataRef.filePath
            if (filePath === '/') {
                filePath = filePath + treeNode.dataRef.fileName
            } else {
                filePath = filePath + '/' + treeNode.dataRef.fileName
            }
            const res = await fileApi.fileTree({
                isDir: 1,
                "filePath": filePath,
                "currentPage": 1,
                "pageCount": 99,

            })
            treeNode.dataRef.children = res.data.records;
            resolve();
        }
    })
}

/**
 * 选择树节点
 * @param selectedKeys 
 * @param e 
 */
function selectTreeNode(selectedKeys, e) {
    // 更新当前选中的节点
    treeState.currentNode = e.node
    
    const data = {
        userFileId: selectedKeys[0],
        filePath: e.node.filePath
    }
    if (!data.filePath) {
        data.filePath = '/'
    } else if (data.filePath === '/') {
        data.filePath = data.filePath + e.node.fileName
    } else {
        data.filePath = data.filePath + '/' + e.node.fileName
    }
    emits('change', data)
}

/**
 * 获取文件夹树数据
 * @param {string} filePath 文件路径，默认为根路径 '/'
 */
function getTree(filePath = '/') {
    return new Promise((resolve, reject) => {
        console.log('📁 获取文件夹树数据，路径:', filePath);
        
        const params = {
            "filePath": filePath,
            "currentPage": 1,
            "pageCount": 99,
            isDir: 1,
        }
        
        fileApi.fileTree(params).then(res => {
            const { records, success } = res.data
            console.log('📁 获取到文件夹记录数:', records?.length || 0);
            
            // 检查当前树状态
            const hasRoot = treeState.list.some(node => node.userFileId === 'root');
            console.log('📁 当前树是否有根节点:', hasRoot);
            
            // 保存当前选中状态，以便刷新后恢复
            const currentSelectedKeys = [...treeState.selectedKeys];
            const currentExpandedKeys = [...treeState.expandedKeys];
            console.log('📁 当前选中节点:', currentSelectedKeys);
            
            if (!hasRoot) {
                // 首次初始化，创建根节点
                console.log('📁 首次初始化，创建根节点');
                const list = [{
                    fileName: '全部文件',
                    userFileId: 'root',
                    isDir: 1,
                    filePath: '/'
                }]
                list[0].children = records;
                
                // 如果没有选中节点，默认选中根节点
                if (currentSelectedKeys.length === 0) {
                    treeState.selectedKeys = ['root'];
                    treeState.currentNode = list[0];
                    
                    // 初始化时发出change事件，让右侧显示根目录内容
                    emits('change', {
                        userFileId: 'root',
                        filePath: '/'
                    });
                }
                
                // 确保根节点展开
                if (!currentExpandedKeys.includes('root')) {
                    treeState.expandedKeys = [...currentExpandedKeys, 'root'];
                } else {
                    treeState.expandedKeys = currentExpandedKeys;
                }
                
                // 更新树数据
                treeState.list = list;
            } else {
                // 已存在根节点，只更新children
                console.log('📁 已存在根节点，只更新子节点');
                const rootNode = treeState.list.find(node => node.userFileId === 'root');
                if (rootNode) {
                    // 更新根节点的子节点
                    rootNode.children = records;
                    
                    // 保持当前选中状态
                    treeState.selectedKeys = currentSelectedKeys;
                    
                    // 确保根节点展开
                    if (!currentExpandedKeys.includes('root')) {
                        treeState.expandedKeys = [...currentExpandedKeys, 'root'];
                    } else {
                        treeState.expandedKeys = currentExpandedKeys;
                    }
                } else {
                    // 异常情况：列表中没有根节点但hasRoot为true
                    console.warn('📁 异常：列表中没有找到根节点，重新创建');
                    const list = [{
                        fileName: '全部文件',
                        userFileId: 'root',
                        isDir: 1,
                        filePath: '/'
                    }];
                    list[0].children = records;
                    
                    // 保持当前选中状态或默认选中根节点
                    if (currentSelectedKeys.length > 0) {
                        treeState.selectedKeys = currentSelectedKeys;
                    } else {
                        treeState.selectedKeys = ['root'];
                        treeState.currentNode = list[0];
                        
                        emits('change', {
                            userFileId: 'root',
                            filePath: '/'
                        });
                    }
                    
                    // 确保根节点展开
                    if (!currentExpandedKeys.includes('root')) {
                        treeState.expandedKeys = [...currentExpandedKeys, 'root'];
                    } else {
                        treeState.expandedKeys = currentExpandedKeys;
                    }
                    
                    // 更新树数据
                    treeState.list = list;
                }
            }
            
            console.log('📁 文件夹树更新完成');
            resolve();
        }).catch(error => {
            console.error('📁 获取文件夹树数据失败:', error);
            message.error('获取文件夹列表失败');
            reject(error);
        });
    });
}

/**
 * 刷新目标节点的children（用于新建文件夹后刷新父节点）
 * @param {Object} targetNode 目标节点
 */
function refreshTargetNode(targetNode) {
    return new Promise(async (resolve, reject) => {
        if (targetNode.userFileId === 'root') {
            // 如果是根节点，使用优化的方法只更新子节点
            console.log('📁 刷新根节点，使用优化方法');
            await updateRootNodeChildren();
            resolve();
            return;
        }
        
        console.log('📁 刷新目标节点:', targetNode.fileName);
        
        // 找到目标节点并清除其children缓存
        const findAndRefreshNode = async (nodes, targetId) => {
            for (let node of nodes) {
                if (node.userFileId === targetId) {
                    console.log('📁 找到目标节点，清除children缓存');
                    // 清除children缓存
                    delete node.children;
                    // 重新加载该节点的children
                    await loadNodeChildren(node);
                    return true;
                }
                if (node.children) {
                    if (await findAndRefreshNode(node.children, targetId)) {
                        return true;
                    }
                }
            }
            return false;
        }
        
        // 如果找不到目标节点，可能是因为树结构发生了变化
        if (!await findAndRefreshNode(treeState.list, targetNode.userFileId)) {
            console.warn('📁 未找到目标节点，可能是树结构已变化');
        }
        resolve();
    });
}

/**
 * 刷新父节点的children（用于删除或重命名文件夹后刷新）
 * @param {Object} childNode 子节点
 */
function refreshParentNode(childNode) {
    // 如果是根节点下的直接子节点，使用优化方法刷新根节点
    if (!childNode.filePath || childNode.filePath === '/') {
        console.log('📁 刷新根节点下的直接子节点，使用优化方法');
        updateRootNodeChildren();
        return;
    }
    
    console.log('📁 查找并刷新父节点:', childNode.fileName);
    
    // 找到父节点并刷新其children
    const findAndRefreshParentNode = (nodes, targetChildNode) => {
        for (let node of nodes) {
            if (node.children) {
                // 检查当前节点的children中是否包含目标子节点
                const hasTargetChild = node.children.some(child => 
                    child.userFileId === targetChildNode.userFileId
                );
                
                if (hasTargetChild) {
                    console.log('📁 找到父节点:', node.fileName);
                    // 找到了父节点，清除其children缓存并重新加载
                    delete node.children;
                    loadNodeChildren(node);
                    return true;
                }
                
                // 递归查找
                if (findAndRefreshParentNode(node.children, targetChildNode)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    // 如果在树中找不到父节点，说明可能是根级别的操作
    if (!findAndRefreshParentNode(treeState.list, childNode)) {
        console.log('📁 未找到父节点，可能是根级别操作，使用优化方法');
        updateRootNodeChildren();
    }
}

/**
 * 加载节点的children数据
 * @param {Object} node 节点对象
 */
async function loadNodeChildren(node) {
    if (node.userFileId === 'root') {
        console.log('📁 加载根节点子节点，使用优化方法');
        updateRootNodeChildren();
        return;
    }
    
    console.log('📁 加载节点子节点:', node.fileName);
    
    let filePath = node.filePath;
    if (filePath === '/') {
        filePath = filePath + node.fileName;
    } else {
        filePath = filePath + '/' + node.fileName;
    }
    
    try {
        const res = await fileApi.fileTree({
            isDir: 1,
            "filePath": filePath,
            "currentPage": 1,
            "pageCount": 99,
        });
        
        if (res.data && res.data.records) {
            console.log('📁 成功加载节点子节点，数量:', res.data.records.length);
            node.children = res.data.records;
        } else {
            console.warn('📁 加载节点子节点返回空数据');
            node.children = [];
        }
    } catch (error) {
        console.error('📁 加载节点子节点失败:', error);
        message.error('加载文件夹失败');
        // 设置为空数组，避免undefined导致的渲染问题
        node.children = [];
    }
}

/**
 * 选择替代文件夹（当删除当前选中的文件夹时）
 * @param {Object} deletedNode 被删除的节点
 */
function selectAlternativeFolder(deletedNode) {
    // 找到被删除节点的父节点和同级节点
    const findParentAndSiblings = (nodes, targetNode, parent = null) => {
        for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            if (node.userFileId === targetNode.userFileId) {
                // 找到了目标节点，返回父节点、同级节点和当前索引
                const siblings = nodes.filter(sibling => sibling.userFileId !== targetNode.userFileId)
                return { parent, siblings, index: i, allNodes: nodes }
            }
            if (node.children && node.children.length > 0) {
                const result = findParentAndSiblings(node.children, targetNode, node)
                if (result) return result
            }
        }
        return null
    }
    
    const result = findParentAndSiblings(treeState.list, deletedNode)
    
    if (result) {
        let newSelectedNode = null
        
        // 1. 优先选择同级别的其他文件夹
        if (result.siblings.length > 0) {
            // 选择被删除节点位置的下一个节点，如果没有则选择上一个节点
            if (result.index < result.siblings.length) {
                newSelectedNode = result.siblings[result.index]
            } else {
                newSelectedNode = result.siblings[result.siblings.length - 1]
            }
        }
        // 2. 如果同级别没有其他文件夹，选择父级文件夹
        else if (result.parent && result.parent.userFileId !== 'root') {
            newSelectedNode = result.parent
        }
        // 3. 如果没有父级文件夹或父级是根节点，选择根节点
        else {
            newSelectedNode = treeState.list[0] // 根节点
        }
        
        if (newSelectedNode) {
            // 更新选中状态
            treeState.selectedKeys = [newSelectedNode.userFileId]
            treeState.currentNode = newSelectedNode
            
            // 构造新的文件夹数据并发出change事件
            const data = {
                userFileId: newSelectedNode.userFileId,
                filePath: newSelectedNode.filePath || '/'
            }
            
            // 如果不是根节点，需要构造完整的文件路径
            if (newSelectedNode.userFileId !== 'root') {
                if (!newSelectedNode.filePath || newSelectedNode.filePath === '/') {
                    data.filePath = '/' + newSelectedNode.fileName
                } else {
                    data.filePath = newSelectedNode.filePath + '/' + newSelectedNode.fileName
                }
            } else {
                // 根节点的路径就是 '/'
                data.filePath = '/'
            }
            
            emits('change', data)
        }
    } else {
        // 如果找不到合适的节点，默认选择根节点
        const rootNode = treeState.list[0]
        if (rootNode) {
            treeState.selectedKeys = [rootNode.userFileId]
            treeState.currentNode = rootNode
            emits('change', {
                userFileId: rootNode.userFileId,
                filePath: '/'
            })
        }
    }
}

function handleMenu(data, type) {
    console.log("🚀 ~ handleMenu ~ data:", data)
    formState.value.filePath = '/'
    if (data.filePath && data.userFileId !== 'root') {
        if( data.filePath === '/') {
            formState.value.filePath = data.filePath + data.fileName
        }else{
            formState.value.filePath = data.filePath +'/'+ data.fileName
        } 
    }
    modalState.detail = data
    switch (type) {
        case 'add':
            modalState.visible = true
            modalState.type = type
            formState.value.fileName = undefined

            break;
        case 'edit':
            modalState.visible = true
            modalState.type = type
            formState.value.fileName = data.fileName

            break;
        case 'delete':
            Modal.confirm({
                title: '提示',
                icon: createVNode(ExclamationCircleOutlined),
                content: '是否确定删除所选项',
                onOk() {
                    deleteFolder(data);
                }
            });

            break;
        case 'download':
            let url = `${window.location.origin}/filetransfer/batchDownloadFile?userFileIds=${data.userFileId}&fileName=${encodeURIComponent(data.fileName)}.zip`;
            window.open(url)
            break;
        default:
            break;
    }
}

function deleteFolder(data) {
    fileApi.batchDeleteFile({ userFileIds: data.userFileId }).then(res => {
        if (res.code === 200) {
            message.success('删除成功')
            
            // 检查被删除的文件夹是否是当前选中的文件夹
            const isCurrentSelected = treeState.selectedKeys.includes(data.userFileId)
            
            // 如果删除的是当前选中的文件夹，需要先选择新的文件夹
            if (isCurrentSelected) {
                selectAlternativeFolder(data)
            }
            
            // 然后清除父节点的children缓存并重新加载
            refreshParentNode(data)
        } else {
            message.error('删除失败：' + (res.message || '未知错误'))
        }
    }).catch(error => {
        console.error('删除文件夹失败:', error)
        message.error('删除失败，请稍后重试')
    })
}

function handleCancel() {
    modalState.visible = false
    formState.value = {
        fileName: undefined,
        filePath: '/'
    }
}

function handleOk() {
    const params = { ...formState.value }
    if (modalState.type === 'add') {
        fileApi.createFold(params).then(res => {
            // 新建文件夹：刷新目标文件夹的children
            refreshTargetNode(modalState.detail)
            handleCancel()
        })
    } else {
        fileApi.renameFile({
            fileName: formState.value.fileName,
            userFileId: modalState.detail.userFileId
        }).then(res => {
            // 重命名文件夹：刷新父节点的children
            refreshParentNode(modalState.detail)
            handleCancel()
        })
    }
}

/**
 * 监听文件上传完成事件，自动刷新文件夹树
 */
function handleFileUploadCompleted() {
    console.log('📁 监听到文件上传完成，刷新文件夹树');
    // 延迟刷新，确保后端数据已经更新
    setTimeout(() => {
        refreshCurrentFolder();
    }, 1000);
}

/**
 * 刷新当前选中的文件夹
 * 优化版本：避免重复创建"全部文件"节点
 */
function refreshCurrentFolder() {
    const currentNode = treeState.currentNode;
    if (!currentNode) {
        console.log('📁 没有当前选中节点，仅更新根节点的子节点');
        // 如果没有当前节点，只更新根节点的子节点，不重新创建根节点
        updateRootNodeChildren();
        return;
    }

    if (currentNode.userFileId === 'root') {
        console.log('📁 当前选中的是根节点，仅更新其子节点');
        // 如果当前选中的是根节点，只更新其子节点，不重新创建根节点
        updateRootNodeChildren();
    } else {
        console.log('📁 刷新当前文件夹:', currentNode.fileName);
        // 如果当前选中的是子文件夹，刷新该文件夹的children
        refreshTargetNode(currentNode);
    }
}

/**
 * 只更新根节点的子节点，不重新创建根节点
 * 这是一个优化函数，避免重复创建"全部文件"节点
 */
function updateRootNodeChildren() {
    return new Promise((resolve, reject) => {
        console.log('📁 更新根节点的子节点');
        
        const params = {
            "filePath": '/',
            "currentPage": 1,
            "pageCount": 99,
            isDir: 1,
        }
        
        fileApi.fileTree(params).then(res => {
            const { records, success } = res.data
            console.log('📁 获取到根节点子文件夹数:', records?.length || 0);
            
            // 查找根节点
            const rootNode = treeState.list.find(node => node.userFileId === 'root');
            if (rootNode) {
                console.log('📁 找到根节点，更新其子节点');
                // 只更新子节点，保持根节点不变
                rootNode.children = records;
                resolve();
            } else {
                console.warn('📁 未找到根节点，需要创建');
                // 如果找不到根节点，调用完整的getTree函数
                getTree().then(resolve).catch(reject);
            }
        }).catch(error => {
            console.error('📁 更新根节点子节点失败:', error);
            message.error('获取文件夹列表失败');
            reject(error);
        });
    });
}

onMounted(() => {
    getTree()
    
    // 监听文件上传完成事件
    emitter.on('file.upload.completed', handleFileUploadCompleted);
    emitter.on('folder.uploaded', handleFolderUploaded);
})

onUnmounted(() => {
    // 清理事件监听器
    emitter.off('file.upload.completed', handleFileUploadCompleted);
    emitter.off('folder.uploaded', handleFolderUploaded);
})

async function handleFolderUploaded({ newFolderPath }) {
    console.log('接收到 folder.uploaded 事件', newFolderPath);

    const parentPath = newFolderPath.substring(0, newFolderPath.lastIndexOf('/')) || '/';
    const findNodeByPath = (nodes, path) => {
        if (path === '/') {
            return nodes.find(n => n.userFileId === 'root');
        }
        for (const node of nodes) {
            const nodePath = (node.filePath === '/' ? '' : node.filePath) + '/' + node.fileName;
            if (nodePath === path) {
                return node;
            }
            if (node.children) {
                const found = findNodeByPath(node.children, path);
                if (found) {
                    return found;
                }
            }
        }
        return null;
    };

    const parentNode = findNodeByPath(treeState.list, parentPath);

    if (parentNode) {
        await refreshTargetNode(parentNode);
        await nextTick();

        const newNode = findNodeByPath(treeState.list, newFolderPath);
        if (newNode) {
            treeState.selectedKeys = [newNode.userFileId];
            treeState.currentNode = newNode;
            emits('change', {
                userFileId: newNode.userFileId,
                filePath: newFolderPath
            });

            if (!treeState.expandedKeys.includes(parentNode.userFileId)) {
                treeState.expandedKeys.push(parentNode.userFileId);
            }
        }
    } else {
        // Fallback to full refresh if parent not found
        await getTree();
        await nextTick();
        const newNode = findNodeByPath(treeState.list, newFolderPath);
        if (newNode) {
            treeState.selectedKeys = [newNode.userFileId];
            treeState.currentNode = newNode;
            emits('change', {
                userFileId: newNode.userFileId,
                filePath: newFolderPath
            });
        }
    }
}
</script>

<template>
    <div class="folder-tree-title">文件夹</div>
    <div>
        <ab-tree :fieldNames="treeState.fieldNames" v-model:expandedKeys="treeState.expandedKeys"
            v-model:selectedKeys="treeState.selectedKeys" :treeData="treeState.list" showIcon blockNode
            :load-data="onLoadTreeData" @select="selectTreeNode">
            <template #title="{ dataRef }">
                <span class="tree-node-title" :title="dataRef.fileName">{{ dataRef.fileName }}</span>
            </template>
            <template #icon="{ dataRef }">
                <SvgIcon v-if="dataRef.isDir !== 1" name="modelIcon" className="tree-node-icon" />
                <SvgIcon v-else className="tree-node-icon" name="folder" />
            </template>
            <template #nodeAction="{ record }">
                <a-dropdown class="model-tree-action-dropdown">
                    <span class="ant-dropdown-link">
                        <MoreOutlined />
                    </span>
                    <template #overlay>
                        <a-menu>
                            <a-menu-item>
                                <a @click.stop="handleMenu(record, 'add')">
                                    <a-flex :gap="4">
                                        <PlusOutlined />
                                        <span>新建</span>
                                    </a-flex>
                                </a>
                            </a-menu-item>
                            <a-menu-item v-if="record.userFileId !== 'root'">
                                <a @click.stop="handleMenu(record, 'edit')">
                                    <a-flex :gap="4">
                                        <EditOutlined />
                                        <span>编辑</span>
                                    </a-flex>
                                </a>
                            </a-menu-item>
                            <a-menu-item v-if="record.userFileId !== 'root'">
                                <a @click.stop="handleMenu(record, 'download')">
                                    <a-flex :gap="4">
                                        <DownloadOutlined />
                                        <span>下载</span>
                                    </a-flex>
                                </a>
                            </a-menu-item>
                            <a-menu-item v-if="record.userFileId !== 'root'">
                                <a @click.stop="handleMenu(record, 'delete')">
                                    <a-flex :gap="4">
                                        <DeleteOutlined />
                                        <span>删除</span>
                                    </a-flex>
                                </a>
                            </a-menu-item>
                        </a-menu>
                    </template>
                </a-dropdown>
            </template>
        </ab-tree>
    </div>

    <a-modal :title="modalState.title" :open="modalState.visible" @cancel="handleCancel" @ok="handleOk">
        <a-form ref="formRef" :model="formState">
            <a-form-item label="文件夹名称" name="fileName" :rules="[{ required: true, message: '请输入文件夹名称' }]">
                <a-input v-model:value="formState.fileName" placeholder="请输入文件夹名称" />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<style scoped lang="less">
.folder-tree-title {
    padding-left: 12px;
    color: #fff;
    margin-bottom: 12px;
}

:deep(.ant-tree) {
    background: transparent;

    .ant-tree-treenode {
        align-items: center;
        padding: 0;

        .model-tree-action-dropdown {
            visibility: hidden;
        }

        &.ant-tree-treenode-selected,
        &:hover {
            background: #1F242F;

            .model-tree-action-dropdown {
                visibility: visible;
            }

        }
    }
}

/* 添加树节点标题样式 */
:deep(.ant-tree-node-content-wrapper) {
    overflow: hidden;
}

:deep(.base-tree-content) {
    padding-right: 0;
}

:deep(.base-tree-node-title) {
    display: inline-block;
    max-width: calc(100% - 28px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}
</style>
