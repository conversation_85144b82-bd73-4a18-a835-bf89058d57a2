<script lang="ts" setup>
  import { ref, reactive, defineProps, onMounted, watch, nextTick } from 'vue';
  import { MoreOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue'
  import abTree from '@/components/tree/index.vue';
  import axios from 'axios'
  import bussinessApi from '@/api/bussiness/bussiness.api';
  import OrganizeApi from '@/api/organize/organize.api';
  import ProtocolApi from '@/api/protocol/protocol.api';
  import breadcrumb from '@/views/home/<USER>/breadcrumb.vue';
  import fileApi from '@/api/file/file.api'
  import { tableMixin } from '@/mixins/table';
  const props = defineProps({
    step: {
      type: String,
      default: ''
    }
  });
  enum contentEnum {
    view = 'VIEW', // 概览
    list = 'LIST', // 主题列表
    detail = 'DETAIL', // 主题对应数据
  }
  const createdTime = ref(null)
  const searchForm = reactive({
    step: props.step,
    domainId: undefined,
    organizeId: undefined,
    username: undefined,
    startTime: undefined,
    systemId: undefined,
    endTime: undefined,
    name: undefined
  })
  const search = ref({})
  // 基本属性
  const state: any = reactive({
    keywords: undefined, // 检索关键字
    // loading: false,
    // id: null,
    // tableName: undefined,
    // type: 1 // 1：域 2: 系统
  })
  const activeKey = ref('1');
  const data = ref([])
  const treeRef = ref(null);
  const treeState: any = reactive({
    expandedKeys: [],
    selectedKeys: [],
    list: [],
    currentNode: {}
  })
  // 组织机构树
  const treeStateOrg: any = reactive({
    expandedKeys: [],
    selectedKeys: [],
    list: [
    ]
  })

  const treeFieldNames = {
    children: 'systems',
    title: 'name',
    key: 'key',
  }
  const columns = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      width: 60,
      fixed: 'left'
    },
    {
      title: '文件全名称',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 180,
      ellipsis: true
    },
    //  {
    //   title: '分域',
    //   dataIndex: 'domainName',
    //   key: 'domainName',
    //   width: 120,
    //   ellipsis: true
    // },
    // {
    //   title: '系统',
    //   dataIndex: 'systemName',
    //   key: 'systemName',
    //   width: 120,
    //   ellipsis: true
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      ellipsis: true
    },
    {
      title: '上传人',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      ellipsis: true
    },
    {
      title: '文件大小',
      dataIndex: 'fileSizeFormatted',
      key: 'fileSizeFormatted',
      width: 100,
      align: 'center'
    },
    {
      title: '版本号',
      dataIndex: 'version',
      key: 'version',
      width: 90,
      align: 'center'
    },
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      width: 120,
      fixed: 'right',
      align: 'center'
    },
  ])

  // 收藏/取消收藏文件
  const follow = async (record) => {
    if (!record || !record.id) {
      message.error('文件信息不完整')
      return
    }

    try {
      // 切换收藏状态 (1-收藏, 0-未收藏)
      const isImportant = record.isImportant ? 0 : 1
      const response = await fileApi.setImportant(record.id, isImportant)
      if (response.code == 200) {
        message.success('操作成功')
        getTableList()
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      message.error('操作失败')
    }
  }
  // 下载文件
  const download = async (file) => {
    try {
      message.success(`开始下载文件：${file.name}`)
      // 使用 axios 进行文件下载，以便处理响应头
      const response = await axios.get(`/api/files/download/${file.id}`, {
        responseType: 'blob', // 设置响应类型为 blob
        headers: {
          'Authorization': localStorage.getItem("token")
        }
      })

      // 创建 Blob URL
      const blob = new Blob([response.data], {
        type: response.headers['content-type']
      })
      const url = window.URL.createObjectURL(blob)
      // 创建一个隐藏的a标签来处理下载
      const link = document.createElement('a')
      link.href = url
      // 使用 encodeURIComponent 对文件名进行编码
      link.download = decodeURIComponent(file.name)
      document.body.appendChild(link)
      link.click()

      // 清理资源
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载文件错误:', error)
      message.error(`下载失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 获取嵌套数组的key
   * @param arr 
   */
  function getTreeNodeKey(arr: any[]) {
    const result = []
    for (const item of arr) {
      if (item.children && Array.isArray(item.children)) {
        const arr: any = getTreeNodeKey(item.children)
        result.push(...arr)
      }
      result.push(item.id);
    }
    return result
  }

  // 监听左侧数据树选择变化
  function selectTreeNode(selectedKeys: any, e: any) {
    console.log("🚀 , e:", e.node, e.node.id);
    const { id, type, business, name } = e.node;
    if (e.node.type == 'other' && e.node.name == '离线数据') {
      searchForm.systemId = e.node.systemId;
    }
    treeState.currentNode = e.node.dataRef;
    console.log(id, type, business, name)
    // state.content = type
    // state.id = id
    // if (business) {
    //   state.type = 2
    // } else {
    //   state.type = 1
    // }
    // if (type === contentEnum.detail) {
    //   state.tableName = name
    // }
  }


  /**
   * 加载树目录数据
   * @param treeNode 
   */
  const onLoadTreeData = (treeNode: any) => {
    return new Promise < void> (async resolve => {
      if (treeNode.dataRef.systems || treeNode.dataRef.isLeaf) {
        resolve();
        return;
      }
      if (!treeNode.dataRef.isLeaf) {
        // const data: any = await ProtocolApi.getProtocolListBySystem(treeNode.dataRef.id)
        treeNode.dataRef.systems = [
          //   {
          //   id: treeNode.dataRef.key + 'SSBW',
          //   key: treeNode.dataRef.key + 'SSBW',
          //   name: 'SSBW数据',
          //   type: 'other',
          //   systemId: treeNode.dataRef.id,
          //   systems: [],
          // }, 
          {
            id: treeNode.dataRef.key + 'LX',
            key: treeNode.dataRef.key + 'LX',
            name: '离线数据',
            type: 'other',
            systemId: treeNode.dataRef.id,
            isLeaf: true,
          }];

        console.log('treeState.selectedKeys', treeState.selectedKeys)
        if (treeState.selectedKeys.length == 0) {
          treeState.selectedKeys = [treeNode.dataRef.key + 'LX'];
          treeState.currentNode = {
            id: treeNode.dataRef.key + 'LX',
            key: treeNode.dataRef.key + 'LX',
            name: '离线数据',
            type: 'other',
            systemId: treeNode.dataRef.id,
            isLeaf: true,
          }
        }
        // if (data.data && data.data.length > 0) {
        //   for (const item of data.data) {
        //     treeNode.dataRef.systems[0].systems.push({
        //       ...item,
        //       type: contentEnum.detail,
        //       isLeaf: true,
        //     })
        //   }
        // } else {
        //   treeNode.dataRef.isLeaf = true
        // }
        resolve();
      }
    })
  }

  async function getTreeData() {
    treeState.list = []
    const res = await bussinessApi.getBussinessSystems()
    const result = res.data;
    if (result && Array.isArray(result)) {
      for (const area of result) {
        let areaObj = {
          ...area,
          type: contentEnum.view,
          key: area.id + area.name,
          region: area.id
        }
        areaObj.systems = area.systems.map((item: any) => {
          return { ...item, type: contentEnum.list, key: item.id + item.name, isLeaf: false, region: area.id, business: item.id }
        })
        treeState.list.push(areaObj)
      }
    }
    console.log('treeState.list', treeState.list);

    if (treeState.expandedKeys.length == 0) {
      treeState.expandedKeys = [treeState.list[0].key, treeState.list[0].systems[0].key];
    }
  }
  async function getOrgTreeData() {
    const res = await OrganizeApi.getOrgTree();
    console.log('organizeList', res);
    treeStateOrg.list = res.data;
    console.log('treeStateOrg.list', treeStateOrg.list);

    if (treeStateOrg.expandedKeys.length == 0) {
      treeStateOrg.expandedKeys = [treeStateOrg.list[0].id];
    }
    if (treeStateOrg.selectedKeys.length == 0) {
      treeStateOrg.selectedKeys = [treeStateOrg.list[0].id];
    }
    getTableList();
  }
  function onSubcribe(record: any, status: number) {
    const params = {
      systemId: record.type == 'LIST' ? record.id : '',
      domainId: record.type == 'VIEW' ? record.id : '',
      protocolId: record.type == 'DETAIL' ? record.id : '',
      status: status
    }
    console.log('params', params);

    ProtocolApi.subscribeProtocol(params).then(res => {
      console.log('订阅参数：', params)
      // state.id = undefined
      message.success('操作成功')
      nextTick(() => {
        // state.content = contentEnum.list
        // state.id = record.id
      })
    })
  }
  watch(() => treeState.expandedKeys, (val => {
    sessionStorage.setItem('treeState.expandedKeys', JSON.stringify(val))
  }), { deep: true })
  watch(() => treeState.selectedKeys, (val => {
    sessionStorage.setItem('treeState.selectedKeys', JSON.stringify(val));
  }), { deep: true })
  watch(() => treeState.currentNode, (val => {
    sessionStorage.setItem('treeState.currentNode', JSON.stringify(val));
    console.log('treeState.currentNodetreeState.currentNode', treeState.currentNode);

    getTableList()
  }), { deep: true })
  watch(() => treeStateOrg.expandedKeys, (val => {
    sessionStorage.setItem('treeStateOrg.expandedKeys', JSON.stringify(val))
  }), { deep: true })
  watch(() => treeStateOrg.selectedKeys, (val => {
    sessionStorage.setItem('treeStateOrg.selectedKeys', JSON.stringify(val));
    getTableList()
  }), { deep: true })
  watch(() => searchForm.systemId, (val => {
    sessionStorage.setItem('searchForm.systemId', val || '')
  }), { deep: true })
  watch(() => activeKey.value, (async val => {
    if (val == '1') {
      await getTreeData();
      getTableList();
    } else {
      getOrgTreeData()
    }
  }), { deep: true })
  onMounted(() => {
    getTreeData()
    const cache = {
      // content: sessionStorage.getItem('state.content'),
      expandedKeys: sessionStorage.getItem('treeState.expandedKeys'),
      selectedKeys: sessionStorage.getItem('treeState.selectedKeys'),
      // tableName: sessionStorage.getItem('state.tableName'),
    }
    // 初始选择预览
    if (cache.expandedKeys) {
      treeState.expandedKeys = JSON.parse(cache.expandedKeys)
    }
    if (cache.selectedKeys) {
      treeState.selectedKeys = JSON.parse(cache.selectedKeys);
    }
    // if (cache.content) {
    //   state.content = cache.content
    // }
    // if (cache.tableName) {
    //   state.tableName = cache.tableName
    // }
    if (sessionStorage.getItem('treeStateOrg.expandedKeys')) {
      treeStateOrg.expandedKeys = JSON.parse(sessionStorage.getItem('treeStateOrg.expandedKeys'))
    }
    if (sessionStorage.getItem('treeStateOrg.selectedKeys')) {
      treeStateOrg.selectedKeys = JSON.parse(sessionStorage.getItem('treeStateOrg.selectedKeys'))
    }
    if (sessionStorage.getItem('treeState.currentNode')) {
      console.log('treeState.currentNode', treeState.currentNode);

      treeState.currentNode = JSON.parse(sessionStorage.getItem('treeState.currentNode'))
      console.log('treeState.currentNode1', treeState.currentNode);
    }
  })
  const filtersClick = (handler: string) => {
    switch (handler) {
      case 'search':
        getTableList()
        break
      case 'reset':
        searchForm.username = undefined; // 清空用户名
        searchForm.startTime = undefined; // 清空开始时间
        searchForm.endTime = undefined; // 清空开始时间
        searchForm.name = undefined; // 清空开始时间
        createdTime.value = null
        getTableList()
        break
    }
  }

  const getTableList = async () => {
    loading.value = true
    try {
      // 分域
      if (createdTime.value) {
        searchForm.startTime = createdTime.value[0]
        searchForm.endTime = createdTime.value[1]
      }
      if (activeKey.value == '1') {
        const current = treeState.currentNode;;
        console.log('currentcurrent', current);
        if (current) {
          if (current.type == 'other' && current.name == '离线数据') {
            searchForm.systemId = current.systemId;
            console.log('searchForm', searchForm);
            const params = {
              pagingSort: {
                currentPage: pagination.value.current,
                pageSize: pagination.value.pageSize
              }, sortList: [], ...searchForm
            }
            fileApi.getFileList(params).then(res => {
              data.value = res.data.records
              pagination.value.total = res.data.total
            })
          } else if (current.type != 'other') {
            // getSSBWData()
          }
        }
      } else {
        searchForm.systemId = undefined;
        searchForm.organizeId = treeStateOrg.selectedKeys[0]
        console.log('searchForm', searchForm);
        const params = { pagingSort: {}, sortList: [], ...searchForm }
        fileApi.getFileList(params).then(res => {
          data.value = res.data.records
          pagination.value.total = res.total
        })
      }

    } catch (error) {
      console.error('获取文件列表失败:', error)
      message.error('获取文件列表失败')
    } finally {
      setTimeout(() => {
        loading.value = false
      }, 300);
    }
  }
  const { pagination, tableHeight, change, loading } = tableMixin({
    getTableList
  });
</script>

<template>
  <a-config-provider>
    <div class="main">
      <div class="left">
        <a-tabs v-model:activeKey="activeKey" size="small">
          <a-tab-pane key="1" tab="分域">
            <!-- <div class="left-head">
              <a-input placeholder="请输入名称进行检索" v-model:value="state.keywords" @pressEnter="handle('search')"
                @change="handle('search')" allow-clear>
                <template #prefix>
                  <SvgIcon name="search" />
                </template>
              </a-input>
            </div> -->
            <div class="left-tree">
              <ab-tree ref="treeRef" :fieldNames="treeFieldNames" v-model:expandedKeys="treeState.expandedKeys"
                v-model:selectedKeys="treeState.selectedKeys" :treeData="treeState.list" showIcon blockNode
                :load-data="onLoadTreeData" @select="selectTreeNode">
                <template #title="{ dataRef }">
                  <span class="tree-node-title" :title="dataRef.name">{{ dataRef.name }}</span>
                </template>
                <template #icon="{ dataRef }">
                  <SvgIcon v-if="dataRef.type === contentEnum.detail" name="modelIcon" className="tree-node-icon" />
                  <SvgIcon v-else className="tree-node-icon" name="folder" />
                </template>
                <template #nodeAction="{ record }">
                  <a-dropdown v-if="record.type !== 'other'" class="model-tree-action-dropdown">
                    <span class="ant-dropdown-link">
                      <MoreOutlined />
                    </span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item>
                          <a @click.stop="onSubcribe(record, 1)">订阅</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a @click.stop="onSubcribe(record, 0)">取消订阅</a>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>
              </ab-tree>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="机构">
            <!-- <div class="left-head">
              <a-input placeholder="请输入名称进行检索" v-model:value="state.keywords" @pressEnter="handleOrg('search')"
                @change="handleOrg('search')" allow-clear>
                <template #prefix>
                  <SvgIcon name="search" />
                </template>
              </a-input>
            </div> -->
            <div class="left-tree">
              <ab-tree ref="treeRef" :fieldNames="{
                  children: 'children',
                  title: 'name',
                  key: 'id'
              }" v-model:expandedKeys="treeStateOrg.expandedKeys" v-model:selectedKeys="treeStateOrg.selectedKeys"
                :treeData="treeStateOrg.list" showIcon blockNode :load-data="onLoadTreeDataOrg"
                @select="selectTreeNode">

                <template #title="{ dataRef }">

                  <span class="tree-node-title" :title="dataRef.name">{{ dataRef.name }}</span>

                </template>
                <template #icon="{ dataRef }">
                  <SvgIcon v-if="dataRef.type === contentEnum.detail" name="modelIcon" className="tree-node-icon" />
                  <SvgIcon v-else className="tree-node-icon" name="folder" />
                </template>
              </ab-tree>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="container">
        <breadcrumb></breadcrumb>
        <div class="content">
          <div class="page-content">
            <div class="page-filters">
              <!-- 第二行栅格：查询条件和按钮 -->
              <a-row :gutter="16">
                <a-col :span="6">
                  <label class="filter-item">
                    <label class="filter-label">文件名：</label>
                    <a-input class="user-input" v-model:value="searchForm.name" placeholder="请输入" style="flex: 1;" />
                  </label>
                </a-col>
                <a-col :span="6">
                  <label class="filter-item">
                    <label class="filter-label">上传人：</label>
                    <a-input v-model:value="searchForm.username" placeholder="请输入" style="flex: 1;" />
                  </label>
                </a-col>

                <a-col :span="6">
                  <label class="filter-item">
                    <label class="filter-label">创建时间：</label>
                    <a-range-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="createdTime"
                      :placeholder="['开始日期', '结束日期']" style="flex: 1;" />
                  </label>
                </a-col>
                <a-col :span="6">
                  <a-space :size="8"> <!-- 按钮右对齐 -->
                    <a-button type="primary" @click="filtersClick('search')">查询</a-button>
                    <a-button @click="filtersClick('reset')">重置</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>
            <ab-spin size="large" v-show="loading"></ab-spin>
            <div class="page-main">
              <a-table v-show="!loading" class="normal-table-component" :columns="columns"
                :scroll="{ y: data.length === 0 ? 'auto' : tableHeight }" :data-source="data" :bodered="false"
                :pagination="pagination" @change="change" :style="{ height: data.length === 0 ? '100%' : '100%' }">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.dataIndex === 'index'">
                    {{ index + 1 }}
                  </template>
                  <template v-if="column.dataIndex === 'operate'">
                    <div class="operate operate-center">
                      <div class="operateIcon icon iconfont FCstar-fill"
                        :style="{ color: record.isImportant === 1 ? '#F79009' : '' }" @click="follow(record)">
                      </div>
                      <a-divider type="vertical" style="border-color:#333741" />
                      <div class="operateIcon icon iconfont FCdownload-2-fill" @click="download(record)">
                      </div>
                    </div>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-config-provider>
</template>

<style scoped lang="less">
  @import './sytle.less';

  .ant-tabs .ant-tabs-tabpane {
    display: flex;
    flex-direction: column;
  }

  /* 添加树节点标题样式 */
  :deep(.ant-tree-node-content-wrapper) {
    overflow: hidden;
  }

  :deep(.base-tree-content) {
    padding-right: 0;
  }

  :deep(.base-tree-node-title) {
    display: inline-block;
    max-width: calc(100% - 28px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }

  /* :deep(.ant-tabs.ant-tabs-top .ant-tabs-tab){
    padding: 0;
  } */
  :deep(.ant-tabs-nav) {
    padding: 0 12px;

  }

  .operate-center {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .operateIcon {
    cursor: pointer;
  }

  .page-filters {
    width: 100%;
    margin-bottom: 16px;
  }

  .filter-item {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  /* 统一标签样式（可选） */

  .page-filters .ant-space {
    width: 100%;

    label {
      width: auto;
      display: inline-block;
    }
  }

  /* 统一表单元素宽度（可选） */
  .page-filters .ant-select,
  .page-filters .ant-select-selector,
  .page-filters .ant-input,
  .page-filters .ant-picker {
    width: 100%;
  }

  /* 统一标签样式 */
  .filter-label {
    /* 设置标签最小宽度 */
    text-align: left;
    /* 标签文字右对齐 */
    font-weight: 500;
    white-space: nowrap;
    /* 防止标签文字换行 */
  }

  /* 统一表单元素样式 */
  .ant-select,
  .ant-input,
  .ant-picker {
    min-width: 0;
    /* 修复某些情况下flex无法收缩的问题 */
  }
</style>