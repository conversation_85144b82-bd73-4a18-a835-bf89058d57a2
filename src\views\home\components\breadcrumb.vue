<template>
    <div class="breadCrumb">
      <a-breadcrumb>
        <a-breadcrumb-item v-for="(item, index) in STATIC_LIST" :key="index">
          <template v-if="item.icon">
            <i class="iconfont " :class="item.icon"></i>
          </template>
          <span class="current" v-else>{{ item.name }}</span>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
  </template>
  
  <script setup>
  import { ref, watch } from "vue";
  import { useBreadcrumbStore } from "@/stores/breadcrumb";
  
  const breadcrumbStore = useBreadcrumbStore();
  const STATIC_LIST = ref([]);
  
  watch(
    () => breadcrumbStore.STATIC,
    (newStatic) => {
      STATIC_LIST.value = [...newStatic];
    },
    { immediate: true }
  );
  </script>
  
  <style scoped lang="less">
  .breadCrumb {
    padding-bottom: 12px;
    :deep(.ant-breadcrumb-link){
      padding: 4px 0
    }
    .iconfont{
      font-size: 20px;
      &::before{
        position: relative;
        top: 2px;
        color: @gray-6
      }
    }
  }
  .current{
    padding: 4px 8px 4px 8px;
    display: inline-block;
    background-color: @gray-10;
    border-radius: 2px;
  }
  :deep(.ant-breadcrumb) ol{
    height: 32px!important;
    li{
      display: flex;
      align-items:center;
    }
  }
  </style>
  