<script lang="ts" setup>
  import SideBar from "./components/SiderBar.component.vue";
  import { onMounted, onUnmounted, ref, watch, nextTick, computed } from 'vue'
  import { addCdnUrl } from '@/common/utils'
  import { useRoute, RouterView } from 'vue-router'
  import { modelBg } from '@/assets/images/base64/logo';
  import fileApi from '@/api/file/file.api'


  const route = useRoute()
  const contentClass = ref('')
  const loading = ref(false)
  const currentTime = ref(getFormattedTime())
  const userInfoName = localStorage.getItem("username");
  let animationFrameId: number | null = null
  function getFormattedTime() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 计算属性用于返回完整的内联样式对象
  const dynamicStyle = computed(() => ({
    background: `url(${modelBg}) lightgray 50% / cover no-repeat`,
  }));

  function updateTime() {
    currentTime.value = getFormattedTime()
    animationFrameId = requestAnimationFrame(updateTime)
  }

  function getOnlineStorage(){
    fileApi.getStorage().then(res => {
      localStorage.setItem('storage',JSON.stringify(res.data))
    })
  }

  // 启动更新
  onMounted(() => {
    //你已经登录
    if (!localStorage.getItem("token")) {
      //   window.location.href =  route.query.goto ? `${route.query.goto}` : "/micro";
      window.location.href = "/login";
    }
    updateTime();
    getOnlineStorage()
  })

  // 组件卸载时取消动画帧
  onUnmounted(() => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
  })
  // 监听路由
  watch(
    () => route.path,
    (newPath, oldPath) => {
      // 数据标准页面样式修改
      loading.value = true;
      sessionStorage.clear();
      if (newPath.indexOf('/metadataStandards/codeStandard') !== -1 || newPath.indexOf('/metadataStandards/fieldStandard') !== -1) {
        contentClass.value = 'r-content'
      } else {
        contentClass.value = ''
      }
      nextTick(() => {
        setTimeout(() => {
          loading.value = false
        }, 200)
      })
    }
  )
  const logo = addCdnUrl(new URL('@/assets/images/logo.png', import.meta.url).href);
  const headerImg = addCdnUrl(new URL('@/assets/images/headerImg.png', import.meta.url).href)
  const logout = () => {
    window.localStorage.removeItem("token");
    // window.localStorage.removeItem("projInfo");
    window.localStorage.removeItem("username");
    window.location.href = "/login?goto=" + window.location.href;
  };
</script>

<template>
  <a-config-provider>
    <a-layout :style="{'height':'100%'}" class="main-layout">
      <a-layout-header class="header">
        <span class="logo" @click="()=>{
            $router.push({ path: '/workbench' })
        }">
          <img style="width: 121px;height: 23px" :src="logo" />
        </span>
        <!-- <image width="40" height="40" src="/assets/images/xl_logo.png" />
        <image width="40" height="40" src="/assets/images/xl_title.png" /> -->
        <div class="header-info">
          <div class="time">
            {{ currentTime }}
          </div>
          <div class="user-info">
            <img class="header-img" :src="headerImg">
            <!-- <a-dropdown :placement="'bottomLeft'"> -->
            <div class="name">
              {{userInfoName||'人员席位'}}
            </div>
            <!-- <a-tooltip> -->
              <span @click="logout"  title="退出登录" class="iconfont FClogin-box logout"></span>
            <!-- </a-tooltip> -->
            <!-- <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a @click="logout">
                      退出登录
                    </a>
                  </a-menu-item>
                </a-menu>
              </template> -->
            <!-- </a-dropdown> -->
          </div>
        </div>
      </a-layout-header>
      <a-layout :style="dynamicStyle">
        <SideBar />
        <a-layout-content :class="contentClass" :style="{margin: 0, height: '100%'}">

          <div class="routerView">
            <a-config-provider>
              <div class="main">
                <ab-spin v-show="loading" size="large"></ab-spin>
                <router-view v-if="!loading" />
              </div>
            </a-config-provider>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-config-provider>
</template>

<style scoped lang="less">
  .main-layout {
    border-top: 1px solid @gray-9;
  }

  .ant-layout-header {
    height: 42px;
    line-height: 42px;
    border-bottom: 1px solid @gray-9;
    padding-inline: 0;
    background-color: @gray-12;
  }


  .system-title {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    margin-left: 16px;
    line-height: 64px;
  }

  .logo {
    padding-left: 12px;
    display: flex;
    align-items: center;
    height: 100%;
    gap: 12px;
    float: left;
  }

  .ant-layout-content {}

  .r-content {
    // background: transparent !important;
    // padding: 16px !important;
  }

  .routerView {
    width: 100%;
    height: 100%;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
  }

  /* .breadCrumb {
    height: 32px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    border-bottom: 1px solid @gray-9;
    background-color: @gray-12;
  } */

  .main {
    height: 100%;
    color: @white;
    color: @gray-5;
  }

  .header-info {
    float: right;

    color: @gray-5;
  }

  .time {
    padding: 0 16px;
    font-weight: 500;
    border-right: 1px solid @gray-9;
    float: left;
    color: @gray-5;
  }

  .user-info {
    float: right;
    padding: 0 16px;
    display: flex;
    align-items: center;
    color: @gray-2;

    .logout {
      margin-left: 16px;
      cursor: pointer;
      color: @gray-5
    }
  }

  .header-img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: red;
  }

  .name {
    padding-left: @padding-lg;
  }
</style>