/**
 * 大文件上传配置
 * 针对10GB-50GB文件优化
 */

export interface UploadConfig {
  // 分片配置
  chunkSize: number; // 分片大小
  md5ChunkSize: number; // MD5计算分片大小
  
  // 并发配置
  simultaneousUploads: number; // 同时上传的分片数
  maxConcurrentUploads: number; // 最大并发数
  
  // 重试配置
  maxChunkRetries: number; // 分片重试次数
  retryDelay: number; // 重试延迟(ms)
  
  // 性能配置
  enableMD5Optimization: boolean; // 启用MD5优化
  enableProgressThrottle: boolean; // 启用进度节流
  progressUpdateInterval: number; // 进度更新间隔(ms)
  
  // 网络配置
  timeout: number; // 请求超时时间(ms)
  keepAlive: boolean; // 保持连接
}

/**
 * 根据文件大小获取优化配置
 */
export function getOptimizedConfig(fileSize: number): UploadConfig {
  const fileSizeGB = fileSize / (1024 * 1024 * 1024);
  
  if (fileSizeGB >= 50) {
    // 50GB以上超大文件 - 大幅增加分片大小
    return {
      chunkSize: 100 * 1024 * 1024, // 100MB分片，减少分片数量
      md5ChunkSize: 50 * 1024 * 1024, // 50MB MD5分片，提升计算效率
      simultaneousUploads: 6, // 适当减少并发，避免内存压力
      maxConcurrentUploads: 6,
      maxChunkRetries: 3,
      retryDelay: 2000,
      enableMD5Optimization: true,
      enableProgressThrottle: true,
      progressUpdateInterval: 1000,
      timeout: 600000, // 10分钟，大分片需要更长时间
      keepAlive: true
    };
  } else if (fileSizeGB >= 10) {
    // 10GB-50GB大文件 - 显著增加分片大小
    return {
      chunkSize: 64 * 1024 * 1024, // 64MB分片，平衡效率和稳定性
      md5ChunkSize: 32 * 1024 * 1024, // 32MB MD5分片
      simultaneousUploads: 4, // 减少并发数，提升稳定性
      maxConcurrentUploads: 4,
      maxChunkRetries: 2,
      retryDelay: 1500,
      enableMD5Optimization: true,
      enableProgressThrottle: true,
      progressUpdateInterval: 800,
      timeout: 480000, // 8分钟
      keepAlive: true
    };
  } else if (fileSizeGB >= 1) {
    // 1GB-10GB中等文件 - 适度增加分片大小
    return {
      chunkSize: 32 * 1024 * 1024, // 32MB分片，提升效率
      md5ChunkSize: 16 * 1024 * 1024, // 16MB MD5分片
      simultaneousUploads: 4,
      maxConcurrentUploads: 4,
      maxChunkRetries: 2,
      retryDelay: 1000,
      enableMD5Optimization: true,
      enableProgressThrottle: false,
      progressUpdateInterval: 500,
      timeout: 300000, // 5分钟
      keepAlive: true
    };
  } else {
    // 1GB以下小文件 - 保持原有配置
    return {
      chunkSize: 10 * 1024 * 1024, // 10MB分片，小文件适中
      md5ChunkSize: 5 * 1024 * 1024, // 5MB MD5分片
      simultaneousUploads: 3,
      maxConcurrentUploads: 3,
      maxChunkRetries: 1,
      retryDelay: 500,
      enableMD5Optimization: false,
      enableProgressThrottle: false,
      progressUpdateInterval: 200,
      timeout: 120000, // 2分钟
      keepAlive: false
    };
  }
}

/**
 * 默认配置（用于向后兼容，针对大文件优化）
 */
export const DEFAULT_CONFIG: UploadConfig = {
  chunkSize: 32 * 1024 * 1024, // 32MB，提升大文件上传效率
  md5ChunkSize: 16 * 1024 * 1024, // 16MB，平衡计算速度和内存使用
  simultaneousUploads: 4, // 减少并发数，提升稳定性
  maxConcurrentUploads: 4,
  maxChunkRetries: 2,
  retryDelay: 1000,
  enableMD5Optimization: true,
  enableProgressThrottle: true,
  progressUpdateInterval: 500,
  timeout: 480000, // 8分钟，适应大分片上传
  keepAlive: true
};

/**
 * 获取网络质量评估配置
 */
export function getNetworkOptimizedConfig(networkSpeed: 'slow' | 'medium' | 'fast'): Partial<UploadConfig> {
  switch (networkSpeed) {
    case 'slow':
      return {
        chunkSize: 5 * 1024 * 1024, // 5MB
        simultaneousUploads: 2,
        maxConcurrentUploads: 2,
        timeout: 600000, // 10分钟
        retryDelay: 3000
      };
    case 'medium':
      return {
        chunkSize: 10 * 1024 * 1024, // 10MB
        simultaneousUploads: 4,
        maxConcurrentUploads: 4,
        timeout: 300000, // 5分钟
        retryDelay: 2000
      };
    case 'fast':
      return {
        chunkSize: 20 * 1024 * 1024, // 20MB
        simultaneousUploads: 8,
        maxConcurrentUploads: 8,
        timeout: 180000, // 3分钟
        retryDelay: 1000
      };
    default:
      return {};
  }
}

/**
 * 格式化文件大小显示
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 估算上传时间
 */
export function estimateUploadTime(fileSize: number, networkSpeed: number): string {
  const seconds = fileSize / networkSpeed;

  if (seconds < 60) {
    return `约 ${Math.ceil(seconds)} 秒`;
  } else if (seconds < 3600) {
    return `约 ${Math.ceil(seconds / 60)} 分钟`;
  } else {
    return `约 ${Math.ceil(seconds / 3600)} 小时`;
  }
}

/**
 * 根据当前页面路径获取step参数
 */
export function getStepFromPath(): string {
  const pathname = window.location.pathname;

  if (pathname.includes('/CHSJ/index')) {
    return 'CHSJ';
  } else if (pathname.includes('/FZTY/index')) {
    return 'FZTY';
  } else if (pathname.includes('/data/offlineFile')) {
    return 'SSDK';
  } else if (pathname.includes('/FPPG/index')) {
    return 'FPPG';
  } else {
    // 默认值，可以根据需要调整
    console.warn(`未识别的页面路径: ${pathname}，使用默认step: CHSJ`);
    return 'CHSJ';
  }
}

/**
 * 验证step参数是否有效
 */
export function validateStep(step: string): boolean {
  const validSteps = ['CHSJ', 'FZTY', 'SSDK', 'FPPG'];
  return validSteps.includes(step);
}

/**
 * 清理文件上传完成后的所有相关数据
 * @param identifier 文件标识符
 */
export async function cleanupCompletedFile(identifier: string): Promise<void> {
  try {
    console.log(`🧹 开始清理完成文件的所有数据: ${identifier}`);

    // 1. 从IndexedDB中删除文件数据
    try {
      const { default: indexedDBUtil } = await import('@/utils/indexedDB');
      await indexedDBUtil.deleteFile(identifier);
      console.log(`✅ 已从IndexedDB清除文件数据: ${identifier}`);
    } catch (error) {
      console.error(`❌ 清除IndexedDB文件数据失败: ${identifier}`, error);
    }

    // 2. 清理文件任务映射
    try {
      const fileTaskMapping = localStorage.getItem('fileTaskMapping');
      if (fileTaskMapping) {
        const mappingObj = JSON.parse(fileTaskMapping);
        if (mappingObj[identifier]) {
          delete mappingObj[identifier];
          localStorage.setItem('fileTaskMapping', JSON.stringify(mappingObj));
          console.log(`✅ 已清理文件任务映射: ${identifier}`);
        }
      }
    } catch (error) {
      console.error(`❌ 清理文件任务映射失败: ${identifier}`, error);
    }

    // 3. 清理上传进度（如果存在uploadStore）
    try {
      // 🔥 重要：不再清理上传进度，而是标记为已完成状态
      // 这样可以防止进度被重置为0%
      try {
        // 标记文件为已完成状态
        const completedFiles = localStorage.getItem('completedFileIdentifiers');
        const completedList = completedFiles ? JSON.parse(completedFiles) : [];
        if (!completedList.includes(identifier)) {
          completedList.push(identifier);
          localStorage.setItem('completedFileIdentifiers', JSON.stringify(completedList));
          console.log(`✅ 已标记文件为已完成状态: ${identifier}`);
        }
        
        // 确保进度显示为100%
        const { useUploadStore } = await import('@/stores/upload');
        const uploadStore = useUploadStore();
        if (uploadStore) {
          uploadStore.updateProgress(identifier, 1);
          console.log(`✅ 已确保进度显示为100%: ${identifier}`);
        }
      } catch (error) {
        console.error(`❌ 标记文件为已完成状态失败: ${identifier}`, error);
      }
    } catch (error) {
      console.error(`❌ 处理上传进度失败: ${identifier}`, error);
    }

    console.log(`🎉 文件数据清理完成: ${identifier}`);

  } catch (error) {
    console.error(`❌ 清理完成文件数据时发生错误: ${identifier}`, error);
    // 不抛出错误，避免影响上传完成流程
  }
}
