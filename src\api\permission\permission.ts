import request from '@/common/request'
import { ApiUrl } from './permission.url'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'
// import { axiosIns } from '@/common/ajax'
import ENV from '@/config'

const trainApi: string = ENV.systemApi
const headers = {
  'Content-Type': 'application/json',
  // tk: localStorage.getItem('token')
}

class PermissionApi {
  /**
   * 获取 权限列表
   * @returns 
   */
  getList = () => {
    return request.get(trainApi + ApiUrl.list, {}, {
      headers
    })
  }
}
const permissionApi = new PermissionApi()
export default permissionApi
