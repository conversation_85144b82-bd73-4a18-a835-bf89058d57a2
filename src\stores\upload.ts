import { ref, computed, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useUploadStore = defineStore('state', () => {

  // 从localStorage恢复进度状态
  const getStoredProgress = () => {
    try {
      const stored = localStorage.getItem('uploadProgress')
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      console.error('恢复上传进度失败:', error)
      return {}
    }
  }

  // 从localStorage恢复进行中的任务ID
  const getStoredInProgressTaskIds = () => {
    try {
      const stored = localStorage.getItem('inProgressTaskIds')
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('恢复进行中任务ID失败:', error)
      return []
    }
  }

  const state:any = reactive({
    progress: getStoredProgress(),
    cancelList: [],
    inProgressTaskIds: getStoredInProgressTaskIds() // 存储进行中的任务ID
  })

  function updateProgress(data: any, progress?: number) {
    // If progress parameter is provided, update single file progress
    if (typeof data === 'string' && typeof progress === 'number') {
      const currentProgress = state.progress[data] || 0;

      // 🔥 重要：检查文件是否已完成，如果已完成则严格保护100%进度
      if (isFileCompleted(data)) {
        if (progress < 1) {
          console.warn(`🛡️ 文件 ${data} 已标记为完成，严格拒绝进度回退到 ${(progress * 100).toFixed(2)}%，强制保持100%`);
          // 强制保持100%进度
          state.progress[data] = 1;
          saveProgressToStorage();
          return;
        } else {
          // 即使是100%的更新，也要确保状态正确
          console.log(`🛡️ 文件 ${data} 已完成，确认保持100%进度`);
          state.progress[data] = 1;
          saveProgressToStorage();
          return;
        }
      }

      // 只有当新进度大于或等于当前进度时才更新，确保进度单调递增
      if (progress >= currentProgress) {
        state.progress[data] = progress;

        // 🔥 重要：如果进度达到100%，立即标记为已完成
        if (progress >= 1) {
          console.log(`🎯 文件 ${data} 进度达到100%，立即标记为已完成状态`);
          markFileAsCompleted(data);
        }

        // 只在进度有显著变化时记录日志（避免过多日志）
        if (Math.abs(progress - currentProgress) > 0.01 || progress === 1) {
          console.log(`📈 单文件进度更新: ${data}, 新进度: ${(progress * 100).toFixed(2)}%, 旧进度: ${(currentProgress * 100).toFixed(2)}%`);
        }
        // 持久化存储进度
        saveProgressToStorage();
      } else {
        // 只在进度差异较大时记录警告日志
        if (currentProgress - progress > 0.05) {
          console.warn(`⚠️ 跳过单文件进度更新: ${data}, 新进度(${(progress * 100).toFixed(2)}%)小于当前进度(${(currentProgress * 100).toFixed(2)}%)`);
        }
      }
      return;
    }
    
    // 批量更新逻辑 - 确保进度单调递增
    let hasUpdates = false;
    
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const newProgress = data[key];
        const currentProgress = state.progress[key] || 0;
        
        // 🔥 重要：检查文件是否已完成，如果已完成则不允许进度回退
        if (isFileCompleted(key) && newProgress < 1) {
          console.warn(`文件 ${key} 已标记为完成，拒绝批量进度回退到 ${(newProgress * 100).toFixed(2)}%，保持100%`);
          // 强制保持100%进度
          state.progress[key] = 1;
          hasUpdates = true;
          continue;
        }
        
        // 跳过已完成且无变化的文件
        if (newProgress == 1 && currentProgress == 1) {
          continue;
        }
        
        // 只有当新进度大于或等于当前进度时才更新
        if (newProgress >= currentProgress) {
          state.progress[key] = newProgress;
          // 只在进度有显著变化时记录日志（避免过多日志）
          if (Math.abs(newProgress - currentProgress) > 0.01 || newProgress === 1) {
            console.log(`批量进度更新: ${key}, 新进度: ${(newProgress * 100).toFixed(2)}%, 旧进度: ${(currentProgress * 100).toFixed(2)}%`);
          }
          hasUpdates = true;
        } else {
          // 只在进度差异较大时记录警告日志
          if (currentProgress - newProgress > 0.05) {
            console.warn(`跳过批量进度更新: ${key}, 新进度(${(newProgress * 100).toFixed(2)}%)小于当前进度(${(currentProgress * 100).toFixed(2)}%)`);
          }
        }
      }
    }
    
    // 只有当有更新时才持久化存储
    if (hasUpdates) {
      saveProgressToStorage();
    }
  }

  function updateCancelList(data: any) {
    state.cancelList = data
  }

  function updateInProgressTaskIds(ids: number[]) {
    state.inProgressTaskIds = ids
    // 持久化存储进行中的任务ID
    saveInProgressTaskIdsToStorage()
  }

  // 持久化存储进度到localStorage
  function saveProgressToStorage() {
    try {
      localStorage.setItem('uploadProgress', JSON.stringify(state.progress))
    } catch (error) {
      console.error('保存上传进度失败:', error)
    }
  }

  // 持久化存储进行中的任务ID到localStorage
  function saveInProgressTaskIdsToStorage() {
    try {
      localStorage.setItem('inProgressTaskIds', JSON.stringify(state.inProgressTaskIds))
    } catch (error) {
      console.error('保存进行中任务ID失败:', error)
    }
  }

  // 清理特定文件的进度
  function clearProgress(identifier: string) {
    if (state.progress[identifier] !== undefined) {
      delete state.progress[identifier]
      saveProgressToStorage()
      console.log(`已清理文件 ${identifier} 的进度`)
    }
  }

  // 清理所有进度
  function clearAllProgress() {
    state.progress = {}
    saveProgressToStorage()
    console.log('已清理所有上传进度')
  }

  // 检查文件是否已完成
  function isFileCompleted(identifier: string): boolean {
    try {
      const completedFiles = localStorage.getItem('completedFileIdentifiers');
      const completedList = completedFiles ? JSON.parse(completedFiles) : [];
      return completedList.includes(identifier);
    } catch (error) {
      console.error('检查文件完成状态失败:', error);
      return false;
    }
  }

  // 标记文件为已完成状态
  function markFileAsCompleted(identifier: string): void {
    try {
      const completedFiles = localStorage.getItem('completedFileIdentifiers');
      const completedList = completedFiles ? JSON.parse(completedFiles) : [];

      if (!completedList.includes(identifier)) {
        completedList.push(identifier);
        localStorage.setItem('completedFileIdentifiers', JSON.stringify(completedList));
        console.log(`🛡️ 文件 ${identifier} 已标记为完成状态，进度将被保护`);
      }
    } catch (error) {
      console.error('标记文件完成状态失败:', error);
    }
  }

  return { 
    state, 
    updateProgress, 
    updateCancelList, 
    updateInProgressTaskIds,
    clearProgress,
    clearAllProgress,
    saveProgressToStorage,
    saveInProgressTaskIdsToStorage
  }
})