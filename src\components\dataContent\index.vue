<script setup>
import { ref, onMounted, computed, defineProps, createVNode, reactive, watch, onUnmounted, nextTick } from 'vue'
import { Modal, message } from 'ant-design-vue'
import {
    ExclamationCircleOutlined,
    UploadOutlined
} from '@ant-design/icons-vue'
import { tableMixin } from '@/mixins/table';
import breadcrumb from '@/views/home/<USER>/breadcrumb.vue';
import fileApi from '@/api/file/file.api'
import importModel from '@/components/importModel.vue'
import axios from 'axios'
import taskTableModal from '@/components/taskTableModal.vue'
import emitter from '@/common/eventBus';

const props = defineProps({
    step: {
        type: String,
        default: ''
    },
    folder: {
        type: Object,
        dafault: null
    }
});
const visible = ref(false)
const searchName = ref('')
const columns = ref([
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        width: 60,
        fixed: 'left'
    },
    {
        title: '文件全名称',
        dataIndex: 'fileName',
        key: 'fileName',
        width: 200,
        ellipsis: true
    },
    // {
    //     title: '分域',
    //     dataIndex: 'domainName',
    //     key: 'domainName',
    //     ellipsis: true
    // },
    // {
    //     title: '系统',
    //     dataIndex: 'systemName',
    //     key: 'systemName',
    //     ellipsis: true
    // },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 160,
        ellipsis: true
    },
    {
        title: '上传人',
        dataIndex: 'username',
        key: 'username',
        width: 150,
        ellipsis: true
    },
    {
        title: '大小',
        dataIndex: 'fileSizeFormatted',
        key: 'fileSizeFormatted',
        width: 100,
        align: 'center'
    },
    {
        title: '版本号',
        dataIndex: 'version',
        key: 'version',
        width: 80,
        align: 'center'
    },
    {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        width: 180,
        fixed: 'right',
        align: 'center'
    },
])
const data = ref([])
const taskModalState = reactive({
    visible: false
})
const editModalState = reactive({
    visible: false,
    fileName: '',
    userFileId: '',
    version: '',
    originalFileName: '',
    originalVersion: ''
})

//todo: CHSJ、FPPG、CHSJ、FZTY阶段只能查看当前登录人上传的文件，现在token已经写死了，后期需要修改
//todo:文件检索使用统一接口、文件上传需要修改

// 获取文件列表
const getTableList = async () => {
    let filePath = props?.folder?.filePath || '/';
    
    // 修正路径，处理"全部文件"特殊情况
    if (filePath === '/全部文件' || filePath === '/全部文件/') {
        filePath = '/';
    }
    
    // 确保filePath以"/"开头
    if (!filePath.startsWith('/')) {
        filePath = '/' + filePath;
    }
    
    loading.value = true
    try {
        const params = {
            // pagingSort: {
            //     currentPage: pagination.value.current,
            //     pageSize: pagination.value.pageSize,
            // }, sortList: [], step: props.step, name: searchName.value
            "fileType": 2,
            "filePath": filePath,
            "currentPage": pagination.value.current,
            "pageCount": pagination.value.pageSize,
            name: searchName.value,
            step: props.step
        }
        fileApi.fileTree(params).then(res => {
            data.value = res.data.records
            pagination.value.total = res.data.total
        })
    } catch (error) {
        console.error('获取文件列表失败:', error)
        message.error('获取文件列表失败')
    } finally {
        setTimeout(() =>
            loading.value = false, 300)
    }
}

// 下载文件
const download = async (file) => {
    let url = `${window.location.origin}/filetransfer/downloadfile?userFileId=${file.userFileId}`;
    window.open(url)

}

// 收藏/取消收藏文件
const follow = async (record) => {
    if (!record || !record.userFileId) {
        message.error('文件信息不完整')
        return
    }

    try {
        // 切换收藏状态 (1-收藏, 0-未收藏)
        const isImportant = record.isImportant ? 0 : 1
        const response = await fileApi.setImportant(record.userFileId, isImportant)
        if (response.code == 200) {
            message.success('操作成功')
            getTableList()
        } else {
            message.error('操作失败')
        }
    } catch (error) {
        console.error('收藏操作失败:', error)
        message.error('操作失败')
    }
}

// 删除文件
const deleteRow = (record) => {
    if (!record || !record.userFileId) {
        message.error('文件信息不完整')
        return
    }
    const deleteFile = async () => {
        try {
            const response = await fileApi.batchDeleteFile({ userFileIds: record.userFileId })

            if (response.code == 200) {
                message.success('删除成功')
                getTableList()
            } else {
                message.error('删除失败')
            }
        } catch (error) {
            console.error('删除文件失败:', error)
            message.error('删除失败')
        }
    }

    Modal.confirm({
        title: '提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: '是否确定删除所选项',
        onOk() {
            deleteFile();
        },
    });
}

// 打开编辑文件名对话框
const openEditModal = (record) => {
    if (!record || !record.userFileId) {
        message.error('文件信息不完整')
        return
    }
    editModalState.fileName = record.fileName
    editModalState.userFileId = record.userFileId
    editModalState.version = record.version || ''
    editModalState.originalFileName = record.fileName
    editModalState.originalVersion = record.version || ''
    editModalState.visible = true
}

// 确认重命名文件
const handleRename = async () => {
    if (!editModalState.fileName.trim()) {
        message.error('文件名不能为空')
        return
    }
    
    try {
        const params = {
            userFileId: editModalState.userFileId
        }
        
        const fileNameChanged = editModalState.fileName !== editModalState.originalFileName
        const versionChanged = editModalState.version !== editModalState.originalVersion
        
        if (fileNameChanged) {
            params.fileName = editModalState.fileName
        }
        
        if (versionChanged) {
            params.version = editModalState.version
        }
        
        if (!fileNameChanged && !versionChanged) {
            message.info('未检测到修改')
            return
        }
        
        const response = await fileApi.renameFile(params)
        
        if (response.code == 200) {
            message.success('编辑成功')
            editModalState.visible = false
            getTableList()
        } else {
            message.error('编辑失败：' + (response.message || '未知错误'))
        }
    } catch (error) {
        console.error('编辑文件失败:', error)
        message.error('编辑失败：' + (error.message || '未知错误'))
    }
}

const filtersClick = (handler) => {
    switch (handler) {
        case 'search':
            getTableList()
            break
        case 'reset':
            searchName.value = ''
            getTableList()
            break
    }
}

const { pagination, tableHeight, change, loading } = tableMixin({
    getTableList
});

function openFileTable() {
    taskModalState.visible = true
}

function onSubmit(payload) {
    console.log('提交文件上传', payload)
    visible.value = false;
    // 打开任务表格模态框
    openFileTable()

    if (payload && payload.isDirectoryUpload) {
        // 文件夹上传成功，需要特殊处理
        handleFolderUploaded(payload.newFolderPath);
    } else {
        // 文件上传后，延迟刷新文件列表，确保数据已更新
        setTimeout(() => {
            console.log('文件上传后延迟刷新文件列表')
            getTableList()
        }, 1500)
    }
}

function onColseTask(){
    taskModalState.visible = false
    getTableList()
}

/**
 * 处理重新上传事件
 */
function onReupload(reuploadInfo) {
    console.log('🔄 接收到重新上传事件，重新上传信息:', reuploadInfo);
    
    // 关闭任务模态框
    taskModalState.visible = false;
    
    // 打开文件上传页面
    visible.value = true;
    
    console.log('🔄 已打开文件上传页面进行重新上传');
}

function handleFolderUploaded(newFolderPath) {
    console.log('新上传的文件夹路径:', newFolderPath);
    // 延迟刷新，确保后端数据已经更新
    setTimeout(() => {
        console.log('文件夹上传完成后，触发 folder.uploaded 事件');
        emitter.emit('folder.uploaded', { newFolderPath });
        getTableList(); // 刷新当前文件列表
    }, 1500);
}

// 处理文件上传完成事件
function handleFileUploadCompleted() {
    console.log('监听到文件上传完成，刷新文件列表');
    // 延迟刷新，确保后端数据已经更新
    setTimeout(() => {
        console.log('文件上传完成后延迟刷新文件列表');
        getTableList();
    }, 1500);
}

watch(() => props.folder, (val) => {
    getTableList()
}, { deep: true })

onMounted(() => {
    // 监听文件上传完成事件
    emitter.on('file.upload.completed', handleFileUploadCompleted);
    
    // 初始加载后强制刷新组件
    nextTick(() => {
        // 强制刷新表格
        const tableElement = document.querySelector('.normal-table-component');
        if (tableElement) {
            tableElement.style.display = 'none';
            setTimeout(() => {
                tableElement.style.display = '';
            }, 10);
        }
    });
})

onUnmounted(() => {
    // 清理事件监听器
    emitter.off('file.upload.completed', handleFileUploadCompleted);
})
</script>
<template>
    <div class="container">
        <breadcrumb></breadcrumb>
        <div class="page-content">
            <div class="page-filters">
                <a-button type="primary" @click="() => {
                    visible = true
                }"> <upload-outlined></upload-outlined>上传文件</a-button>
                <a-space>
                    <label>文件检索：</label>
                    <a-input v-model:value="searchName" placeholder="请输入"></a-input>
                    <a-button type="primary" @click="filtersClick('search')">查询</a-button>
                    <a-button @click="filtersClick('reset')">重置</a-button>
                </a-space>
            </div>
            <ab-spin size="large" v-show="loading"></ab-spin>
            <div class="page-main">
                <a-table v-show="!loading" class="normal-table-component" :columns="columns"
                    :scroll="{ y: data.length === 0 ? 'auto' : tableHeight }" :data-source="data" :bodered="false"
                    :pagination="pagination" @change="change" :style="{ height: data.length === 0 ? '100%' : '100%' }" :key="Date.now()">
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </template>
                        <template v-if="column.dataIndex === 'operate'">
                            <div class="operate" style="display: flex; justify-content: center; gap: 5px;">
                                <div class="operateIcon icon iconfont FCdownload-2-fill" @click="download(record)" title="下载">
                                </div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateIcon icon iconfont FCfile-edit-fill" @click="openEditModal(record)" title="编辑" style="font-size: 16px; color: #1890ff;">
                                </div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateIcon icon iconfont FCstar-fill"
                                    :style="{ color: record.isImportant === 1 ? '#F79009' : '' }"
                                    @click="follow(record)" title="收藏">
                                </div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateIcon icon iconfont FCdelete-bin-2-fill" @click="deleteRow(record)" title="删除">
                                </div>
                            </div>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
    </div>
    <importModel :visible="visible" :folder="props.folder" :step="props.step" @close="() => { visible = false; }"
        @ok="onSubmit">
    </importModel>

    <!-- 添加编辑文件名对话框 -->
    <a-modal
        v-model:visible="editModalState.visible"
        title="编辑文件"
        @ok="handleRename"
        :maskClosable="false"
        width="400px"
    >
        <a-form layout="vertical">
            <a-form-item label="文件名" :label-col="{ span: 24 }" style="margin-bottom: 24px;">
                <a-input v-model:value="editModalState.fileName" placeholder="请输入文件名" style="margin-top: 8px;" />
            </a-form-item>
            <a-form-item label="版本号" :label-col="{ span: 24 }" style="margin-bottom: 24px;">
                <a-input v-model:value="editModalState.version" placeholder="请输入版本号" style="margin-top: 8px;" />
            </a-form-item>
        </a-form>
    </a-modal>

    <a-float-button style="right: 16px;" @click="openFileTable" :badge="{ count: 0 }" />

    <taskTableModal :visible="taskModalState.visible" @close="onColseTask" @reupload="onReupload" />
</template>
<style lang="less" scoped>
@import url('./style.less');

.container {
    padding: 16px;
}

.page-content {
    height: calc(100% - 44px);
    background-color: rgba(12, 17, 29, 0.7);
    padding: 16px;
    border: 1px solid @gray-9;
    border-radius: 2px;
}

.operateIcon {
    color: @gray-6;
    cursor: pointer;
}

.page-main {
    height: calc(100% - 32px - 16px);
}

.page-filters {
    width: 100%;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
}

::v-deep .ant-float-btn-body {
    background-color: rgba(12, 17, 29, 1);
    border: 1px solid @gray-9;
    transition: none;

    &:hover {
        background-color: rgba(12, 17, 29, .8);
    }

    .ant-float-btn-icon {
        color: #fff !important;
    }
}
</style>