{"name": "db-sysmanagement-frontend", "version": "0.0.1", "private": true, "type": "module", "scripts": {"start:pre": "npm i --legacy-peer-deps", "start": "npm run dev", "dev": "vite --port 7020", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "cdn_push": "vite build && node getHashList.js && node nodeScp.js"}, "dependencies": {"@antv/g2": "^5.2.11", "amber-design-pro": "^1.0.4", "ant-design-vue": "^4.2.6", "axios": "^1.6.8", "echarts": "^5.5.1", "epic-designer": "^1.0.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "node-scp": "^0.0.23", "pinia": "^2.1.7", "spark-md5": "^3.0.2", "vite-plugin-mock": "^3.0.2", "vue": "^3.4.21", "vue-axios": "^3.5.2", "vue-router": "^4.3.0", "vue-simple-uploader": "^1.0.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/node": "^20.12.5", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "less": "^4.2.0", "less-loader": "^12.2.0", "npm-run-all2": "^6.1.2", "prettier": "^3.2.5", "typescript": "~5.4.0", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.8", "vite-plugin-vue-devtools": "^7.0.25", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.0.11"}}