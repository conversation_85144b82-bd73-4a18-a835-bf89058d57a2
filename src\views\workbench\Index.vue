<script lang="ts" setup>
  import { addCdnUrl } from '@/common/utils'
  import { reactive, createVNode, onMounted, getCurrentInstance, ref, computed, onUnmounted } from 'vue'
  import { useRouter } from 'vue-router'

  import { modelBg } from '@/assets/images/base64/logo';
  const headBg = addCdnUrl(new URL(`./img/background.png`, import.meta.url).href)
  const sjyjBg = addCdnUrl(new URL(`./img/sjyj.png`, import.meta.url).href)
  const xcgxBg = addCdnUrl(new URL(`./img/xcgx.png`, import.meta.url).href)
  const $router = useRouter()
  const instance = getCurrentInstance();
  const currentTime = ref(getFormattedTime())
  let animationFrameId: number | null = null

  const userInfoName = localStorage.getItem("username");
  const dynamicStyle = computed(() => ({
    background: `url(${modelBg}) lightgray 50% / cover no-repeat`,
  }));
  function getFormattedTime() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  // 处理项目弹窗
  const modalState = reactive({
    open: false,
    path: ''
  })
  function navigatorApp() {
    $router.push({ path: modalState.path })
  }

   function updateTime() {
    currentTime.value = getFormattedTime()
    animationFrameId = requestAnimationFrame(updateTime)
  }

  onMounted(() => {
    // document.title = "登录";

    //你已经登录
    if (!localStorage.getItem("token")) {
      //   window.location.href =  route.query.goto ? `${route.query.goto}` : "/micro";
      window.location.href = "/login";
    }
    updateTime();
  });
  const logo = addCdnUrl(new URL('@/assets/images/logo.png', import.meta.url).href);
  const headerImg = addCdnUrl(new URL('@/assets/images/headerImg.png', import.meta.url).href)
  const logout = () => {
    window.localStorage.removeItem("token");
    // window.localStorage.removeItem("projInfo");
    window.localStorage.removeItem("username");
    window.location.href = "/login?goto=" + window.location.href;
  };

  // 组件卸载时取消动画帧
  onUnmounted(() => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
  })
</script>

<template>
  <a-layout :style="{'height':'100%'}" class="main-layout">
    <a-layout-header class="header">
      <span class="logo" @click="()=>{
            $router.push({ path: '/workbench' })
        }">
        <img style="width: 121px;height: 23px" :src="logo" />
      </span>
      <!-- <image width="40" height="40" src="/assets/images/xl_logo.png" />
        <image width="40" height="40" src="/assets/images/xl_title.png" /> -->
      <div class="header-info">
        <div class="time">
          {{ currentTime }}
        </div>
        <div class="user-info">
          <img class="header-img" :src="headerImg">
          <!-- <a-dropdown :placement="'bottomLeft'"> -->
          <div class="name">
            {{userInfoName||'人员席位'}}
          </div>
          <!-- <a-tooltip title="退出登录"> -->
            <span @click="logout"  title="退出登录" class="iconfont FClogin-box logout"></span>
          <!-- </a-tooltip> -->
          <!-- <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a @click="logout">
                    退出登录
                  </a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown> -->
        </div>
      </div>
    </a-layout-header>
    <div class="workbench" :style="dynamicStyle">
      <div class="workbench-content">
        <div @click="()=>{
        $router.push({ path: '/CHSJ/index' })
      }" :style="{background: `url(${sjyjBg}) lightgray 50% / cover no-repeat`}" class="workbench-content-item">
          <span>
            数据引接
          </span>
        </div>
        <div @click="()=>{
        $router.push({ path: '/comprehensiveQuery/index' })
      }
      " :style="{background: `url(${xcgxBg}) lightgray 50% / cover no-repeat`}"><span>
            查询共享</span>
        </div>
      </div>
    </div>
  </a-layout>
</template>

<style lang="less" scoped>
  .main-layout {
    border-top: 1px solid @gray-9;
  }

  .ant-layout-header {
    height: 42px;
    line-height: 42px;
    border-bottom: 1px solid @gray-9;
    padding-inline: 0;
    background-color: @gray-12;
  }

  .header-info {
    float: right;
    color: @gray-5;
  }

  .time {
    padding: 0 16px;
    font-weight: 500;
    border-right: 1px solid @gray-9;
    float: left;
    color: @gray-5;
    font-size: 14px;
  }

  .user-info {
    float: right;
    padding: 0 16px;
    display: flex;
    align-items: center;
    color: @gray-2;
    font-size: 14px;

    .logout {
      margin-left: 16px;
      cursor: pointer;
      color: @gray-5
    }
  }

  .header-img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: red;
  }

  .name {
    padding-left: @padding-lg;
  }

  .system-title {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    margin-left: 16px;
    line-height: 64px;
  }

  .logo {
    padding-left: 12px;
    display: flex;
    align-items: center;
    height: 100%;
    gap: 12px;
    float: left;
  }

  .workbench {
    width: 100%;
    height: calc(100% - 43px);
    overflow: auto;

    &-content {
      background-color: rgba(#0c111d, 0.8) !important;
      display: flex;
      gap: 32px;
      height: 100%;
      ;
      align-items: center;
      justify-content: center;
      color: @white;

      &>div {
        height: 66%;
        width: 30%;
        border: 1px solid @gray-10;
        position: relative;
        cursor: pointer;

        &>span {
          position: absolute;
          bottom: 32px;
          left: 32px;
          font-weight: bold;
          font-size: 30px;
        }
      }
    }
  }
</style>