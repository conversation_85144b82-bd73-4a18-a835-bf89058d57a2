<script setup>
import { ref, defineProps, defineEmits, onMounted, createVNode, reactive, defineExpose, watch } from 'vue';
import { abNotification } from 'amber-design-pro'
import {
  ExclamationCircleOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import { message, Modal, notification } from 'ant-design-vue'
import fileApi from '@/api/file/file.api'
import bussinessApi from '@/api/bussiness/bussiness.api';
import fileSplitUploader from '@/components/fileSplitUploader.vue'

const emits = defineEmits(['close', 'ok'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  step: {
    type: String,
    default: 'CHSJ'
  },
  folder: {
    type: Object,
    default: null
  }
});

const formRef = ref();
const formState = ref({
  domainId: undefined,
  systemId: undefined,
  fileRecordList: [],
  version: undefined,
})
const businessList = ref([])
const systemList = ref([])
const loading = ref(false)
const domainSystemList = ref([])
const splitUploaderRef = ref(null)
const uploadParams = ref({
  domainId: undefined,
  systemId: undefined,
  version: undefined,
  step: undefined,
  filePath: '/'
})

const handleOk = (e) => {
  loading.value = true
  formRef.value.validate().then(async () => {
    console.log('values', formState);
    // 检查是否是文件夹上传
    const isDirectoryUpload = formState.value.fileRecordList.some(
      file => file.relativePath && file.relativePath !== file.name
    );
    
    console.log('是否是文件夹上传:', isDirectoryUpload);
    
    // 定义在外部，以便后续代码可以访问
    let params;
    let newlyUploadedFolder = null;
    
    // 获取当前路径
    const currentPath = props?.folder?.filePath || '/';
    console.log('当前路径:', currentPath);

    // 检查当前路径是否是根目录且显示为"全部文件"
    // 这种情况下应该直接使用"/"作为当前路径，避免创建额外的"全部文件"目录
    const finalCurrentPath = (currentPath === '/全部文件' || currentPath === '/全部文件/') ? '/' : currentPath;
    console.log('优化后的当前路径:', finalCurrentPath);
    
    // 如果是文件夹上传，需要特殊处理
    if (isDirectoryUpload) {
      try {
        // 1. 首先创建文件夹结构
        // 获取所有文件的相对路径并提取出需要的文件夹结构
        const folderPaths = new Set();
        formState.value.fileRecordList.forEach(file => {
          if (file.relativePath) {
            // 提取文件的路径部分（不包括文件名）
            const parts = file.relativePath.split('/');
            if (parts.length > 1) { // 有嵌套结构
              // 逐级构建文件夹路径
              let currentPath = parts[0];
              folderPaths.add(currentPath);
              
              for (let i = 1; i < parts.length - 1; i++) {
                currentPath = currentPath + '/' + parts[i];
                folderPaths.add(currentPath);
              }
            } else {
              // 只有一级文件夹
              folderPaths.add(parts[0]);
            }
          }
        });
        
        console.log('需要创建的文件夹路径:', Array.from(folderPaths));
        
        // 2. 创建文件夹结构
        // 先按照深度排序，确保先创建父文件夹再创建子文件夹
        const sortedFolderPaths = Array.from(folderPaths).sort((a, b) => {
          return a.split('/').length - b.split('/').length;
        });
        
        // 3. 对于每个文件夹路径，检查是否需要创建
        for (const folderPath of sortedFolderPaths) {
          // 基础路径应该保留前导斜杠
          const basePath = finalCurrentPath;
          
          // 构建完整路径，确保以"/"开头
          let fullPathParent;
          if (folderPath.includes('/')) {
            // 多级目录结构
            fullPathParent = basePath === '/' ? 
              '/' + folderPath.substring(0, folderPath.lastIndexOf('/')) : 
              basePath + '/' + folderPath.substring(0, folderPath.lastIndexOf('/'));
          } else {
            // 单级目录结构
            fullPathParent = basePath;
          }
          
          // 确保父路径以"/"开头
          if (!fullPathParent.startsWith('/')) {
            fullPathParent = '/' + fullPathParent;
          }
          
          // 如果父路径不是根目录，移除结尾的斜杠（如果存在）
          if (fullPathParent !== '/' && fullPathParent.endsWith('/')) {
            fullPathParent = fullPathParent.slice(0, -1);
          }
          
          const folderName = folderPath.split('/').pop(); // 取最后一部分作为文件夹名
          
          console.log(`准备创建文件夹: ${folderName}, 父路径: ${fullPathParent}`);
          
          try {
            // 使用已有的创建文件夹API
            await fileApi.createFold({
              fileName: folderName,
              filePath: fullPathParent
            });
            console.log(`成功创建文件夹: ${folderName} 在 ${fullPathParent}`);
          } catch (error) {
            // 如果文件夹已存在，忽略错误继续执行
            console.warn(`创建文件夹失败 ${folderName}，可能已存在:`, error);
          }
        }
      } catch (error) {
        console.error('创建文件夹结构失败:', error);
        message.error('创建文件夹结构失败，请重试');
        loading.value = false;
        return;
      }
      
      // 获取所有文件的第一级目录名称（文件夹名称）
      const folderNames = new Set();
      formState.value.fileRecordList.forEach(file => {
        if (file.relativePath) {
          const folderName = file.relativePath.split('/')[0];
          folderNames.add(folderName);
        }
      });
      
      console.log('检测到的文件夹名称:', Array.from(folderNames));
      
      // 如果检测到多个文件夹，给出警告
      if (folderNames.size > 1) {
        console.warn('检测到多个文件夹上传，这可能导致路径问题');
      }

      if (folderNames.size === 1) {
        const folderName = Array.from(folderNames)[0];
        if (finalCurrentPath === '/') {
          newlyUploadedFolder = `/${folderName}`;
        } else {
          newlyUploadedFolder = `${finalCurrentPath.endsWith('/') ? finalCurrentPath.slice(0, -1) : finalCurrentPath}/${folderName}`;
        }
      } else {
        newlyUploadedFolder = finalCurrentPath;
      }
      
      // 构建参数
      params = {
        ...formState.value,
        step: props.step,
        fileRecordList: formState.value.fileRecordList.map(item => {
          // 如果没有relativePath或者relativePath等于name，说明是单个文件
          if (!item.relativePath || item.relativePath === item.name) {
            return {
              identifier: item.uniqueIdentifier,
              fileName: item.name,
              filePath: finalCurrentPath,  // 保留前导斜杠
              fileSize: item.size
            };
          }
          
          // 处理文件夹内的文件
          const folderName = item.relativePath.split('/')[0];
          const pathInsideFolder = item.relativePath.substring(folderName.length + 1); // 去掉文件夹名称和斜杠
          
          // 构建最终路径，确保以"/"开头
          let finalPath;
          if (finalCurrentPath === '/') {
            // 根目录下上传
            if (pathInsideFolder) {
              // 有子目录的情况，保留完整的相对路径结构
              finalPath = '/' + item.relativePath.substring(0, item.relativePath.lastIndexOf('/'));
            } else {
              // 只有一级目录，直接使用文件夹名
              finalPath = '/' + folderName;
            }
          } else {
            // 非根目录下上传，需要拼接当前目录和相对路径
            if (pathInsideFolder) {
              // 有子目录的情况
              const currentWithoutTrailingSlash = finalCurrentPath.endsWith('/') ? 
                finalCurrentPath.slice(0, -1) : finalCurrentPath;
              finalPath = currentWithoutTrailingSlash + '/' + item.relativePath.substring(0, item.relativePath.lastIndexOf('/'));
            } else {
              // 只有一级目录
              const currentWithoutTrailingSlash = finalCurrentPath.endsWith('/') ? 
                finalCurrentPath.slice(0, -1) : finalCurrentPath;
              finalPath = currentWithoutTrailingSlash + '/' + folderName;
            }
          }
          
          console.log(`文件 ${item.name} 的路径处理:`, {
            relativePath: item.relativePath,
            folderName: folderName,
            pathInsideFolder: pathInsideFolder,
            finalPath: finalPath
          });
          
          return {
            identifier: item.uniqueIdentifier,
            fileName: item.name,
            filePath: finalPath,
            fileSize: item.size
          };
        })
      };
    } else {
      // 普通文件上传，使用简单的路径处理
      params = {
        ...formState.value,
        step: props.step,
        fileRecordList: formState.value.fileRecordList.map(item => {
          const path = props?.folder?.filePath || '/';
          // 确保普通文件上传也正确处理"全部文件"路径
          const finalPath = (path === '/全部文件' || path === '/全部文件/') ? '/' : path;
          
          return {
            identifier: item.uniqueIdentifier,
            fileName: item.name,
            filePath: finalPath, // 保留前导斜杠
            fileSize: item.size
          };
        })
      };
    }
    
    // 打印最终的参数，用于调试
    console.log('最终的保存文件参数:', JSON.stringify(params, null, 2));
    console.log('检查参数是否包含relativePath字段:', 
      params.fileRecordList.some(item => 'relativePath' in item) ? '是，需要移除' : '否，已正确移除');
    
    uploadParams.value.domainId = formState.value.domainId
    uploadParams.value.systemId = formState.value.systemId
    uploadParams.value.version = formState.value.version

    fileApi.saveFile(params).then(res => {
      if (res.success) {
        localStorage.setItem('currentBatchId', res.data)
      }
      emits('ok', { isDirectoryUpload, newFolderPath: newlyUploadedFolder })
      splitUploaderRef.value?.resumeUpload()
      // 只清空文件列表和版本号，保留分域和系统的选择
      formState.value.fileRecordList = []
      formState.value.version = undefined

    }).finally(() => {
      loading.value = false
    })

  }).catch(() => {
    loading.value = false
  })
};

const cancel = () => {
  emits('close')
  // 只清空文件列表和版本号，保留分域和系统的选择
  formState.value.fileRecordList = []
  formState.value.version = undefined

  splitUploaderRef.value?.cancelUpload()
}

const changeDomain = (e) => {
  console.log('changeDomain', formState.value.domainId);
  formState.value.systemId = undefined;
  // 当分域改变时，清除保存的系统选择
  localStorage.removeItem('fileUpload_lastSystemId')
  localStorage.removeItem('fileUpload_lastSystemName')
  
  domainSystemList.value.forEach(c => {
    if (c.id == formState.value.domainId) {
      systemList.value = c.systems.map(c => {
        return {
          label: c.name,
          value: c.id
        }
      })
    }
  })
  // 保存选择的分域ID和名称到 localStorage
  if (formState.value.domainId) {
    const selectedDomain = businessList.value.find(item => item.value == formState.value.domainId)
    if (selectedDomain) {
      localStorage.setItem('fileUpload_lastDomainId', formState.value.domainId)
      localStorage.setItem('fileUpload_lastDomainName', selectedDomain.label)
    }
  }
}

const changeSystem = (e) => {
  // 保存选择的系统ID和名称到 localStorage
  if (formState.value.systemId) {
    const selectedSystem = systemList.value.find(item => item.value == formState.value.systemId)
    if (selectedSystem) {
      localStorage.setItem('fileUpload_lastSystemId', formState.value.systemId)
      localStorage.setItem('fileUpload_lastSystemName', selectedSystem.label)
    }
  }
}

// 恢复用户之前的选择
const restoreUserSelections = () => {
  const lastDomainId = localStorage.getItem('fileUpload_lastDomainId')
  const lastDomainName = localStorage.getItem('fileUpload_lastDomainName')
  const lastSystemId = localStorage.getItem('fileUpload_lastSystemId')
  const lastSystemName = localStorage.getItem('fileUpload_lastSystemName')
  
  // 优先使用名称匹配，如果名称匹配失败则使用ID匹配
  let domainToRestore = null
  if (lastDomainName) {
    domainToRestore = businessList.value.find(item => item.label === lastDomainName)
  }
  if (!domainToRestore && lastDomainId) {
    domainToRestore = businessList.value.find(item => item.value == lastDomainId)
  }
  
  if (domainToRestore) {
    formState.value.domainId = domainToRestore.value
    // 触发分域变化，更新系统列表，但不保存到localStorage（避免重复保存）
    formState.value.systemId = undefined;
    domainSystemList.value.forEach(c => {
      if (c.id == domainToRestore.value) {
        systemList.value = c.systems.map(c => {
          return {
            label: c.name,
            value: c.id
          }
        })
      }
    })
    
    // 恢复系统选择
    if (lastSystemName || lastSystemId) {
      // 需要等待系统列表更新后再设置
      setTimeout(() => {
        let systemToRestore = null
        if (lastSystemName) {
          systemToRestore = systemList.value.find(item => item.label === lastSystemName)
        }
        if (!systemToRestore && lastSystemId) {
          systemToRestore = systemList.value.find(item => item.value == lastSystemId)
        }
        
        if (systemToRestore) {
          formState.value.systemId = systemToRestore.value
        }
      }, 0)
    }
  }
}

watch(() => props.visible, (val) => {
  if (val) {
    uploadParams.value.step = props.step
    uploadParams.value.filePath = props?.folder?.filePath || '/'
    
    // 修正路径，处理"全部文件"特殊情况
    if (uploadParams.value.filePath === '/全部文件' || uploadParams.value.filePath === '/全部文件/') {
      uploadParams.value.filePath = '/';
    }
    
    // 确保filePath以"/"开头
    if (!uploadParams.value.filePath.startsWith('/')) {
      uploadParams.value.filePath = '/' + uploadParams.value.filePath;
    }
    
    // 版本号每次都清空，不记住
    formState.value.version = undefined
    
    // 如果业务列表已加载，立即恢复选择；否则等待加载完成
    if (businessList.value.length > 0) {
      restoreUserSelections()
    }
  }
})

onMounted(async () => {
  const res = await bussinessApi.getBussinessSystems();
  domainSystemList.value = res.data;
  businessList.value = res.data.map(c => {
    return {
      label: c.name,
      value: c.id
    }
  })
  
  // 如果弹窗已经打开，则恢复用户选择
  if (props.visible) {
    restoreUserSelections()
  }
})


</script>
<template>
  <a-modal :open="props.visible" wrapClassName="middle-modal" title="文件上传" @ok="handleOk" @cancel="cancel"
    :loading="loading">
    <a-form ref="formRef" :model="formState" name="basic" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }"
      autocomplete="off">
      <a-form-item class="first-item" label="选择分域" name="domainId">
        <a-select v-model:value="formState.domainId" placeholder="请选择" :options="businessList" @change="changeDomain" />
      </a-form-item>
      <a-form-item label="选择系统" name="systemId">
        <a-select v-model:value="formState.systemId" placeholder="请选择" :options="systemList" @change="changeSystem" />
      </a-form-item>
      <a-form-item label="版本号" name="version">
        <a-input v-model:value="formState.version" placeholder="请输入版本号" />
      </a-form-item>
      <a-form-item label="上传" name="fileRecordList" :rules="[{ required: true, message: '请上传文件/文件夹' }]">
        <fileSplitUploader ref="splitUploaderRef" :params="uploadParams" v-model:value="formState.fileRecordList" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button key="back" @click="cancel">取消</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>

<style lang="less" scoped>
@import 'amber-design-pro/dist/styles/default.less';

::v-deep {

  .ant-form-item:not(.first-item) .ant-form-item-label {
    padding-top: 16px !important;
  }

  .ant-upload-wrapper .ant-upload-drag:not(:hover) p {

    &.ant-upload-text,
    &.ant-upload-hint {
      color: @gray-5;
    }
  }

  .ant-upload-wrapper .ant-upload-drag:hover p {

    &.ant-upload-text,
    &.ant-upload-hint {
      color: @blue-6;
    }
  }

  .ant-upload-list-item-container {
    color: @gray-5;
  }

}

.ant-input {
  height: 32px;
  line-height: 24px;
  font-size: 16px;
  border-radius: 2px;
}
</style>
<style lang="less">
.ant-modal .ant-modal-title {
  font-size: 16px !important;
  line-height: 1 !important;
}
</style>