export enum FileApiUrl {
   filesUpload = '/api/files/upload',
   checkFileExist = '/api/files/checkFileExist',
   fileList = '/file/getfilelist',
   fileDelete = '/api/files/remove/{id}',
   setImportant = '/file/{userFileId}/{status}',
   fileDownload = '/api/files/download/{id}',
   fileSearch = '/api/files/search',
   createFold = '/file/createFold',
   fileTree = '/file/getfilelist',
   renameFile = '/file/renamefile',
   batchDeleteFile = '/file/batchdeletefile',
   uploadFolder = '/filetransfer/uploadfile',
   batchDownloadFile = '/filetransfer/batchDownloadFile',
   getStorage = '/filetransfer/getstorage',
   saveFile = '/fileTask/create',
   listByBatchId = '/fileTask/listByBatchId',
   batchDelete = '/fileTask/batchDelete',
   batchCancel = '/fileTask/setCancelStatus',
   batchPause = '/fileTask/setPauseStatus',
   batchUpload = '/fileTask/setUploadStatus',
   getUploadedChunks = '/fileTask/getUploadedChunks',
   batchSuccess = '/fileTask/setSuccessStatus',

}