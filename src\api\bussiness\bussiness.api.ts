import request from '@/common/request'
import { ApiUrl } from './bussiness.url'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'

class BussinessApi {
  /**
   * 获取域、系统树
   * @returns 
   */
  getBussinessSystems = () => request.get(ApiUrl.getBussinessSystems)

  /**
   * 根据系统id获取主题
   * @param id 系统id
   * @returns 
   */
  getBussinessProtocols = (id: any, type: any) => {
    return request.get(ApiUrl.getBussinessProtocols.replace('{id}', id).replace('{type}', type))
  }

  /**
   * 获取数据概览 统计图数据
   * @returns 
   */
  getSystemOverview = () => request.get(ApiUrl.getSystemOverview)

  /**
   * 获取概览数据列表
   * @param data 翻页参数
   * @returns 
   */
  getSystemDetails = (data: { pageNum: any, pageSize: any }) => {
    return request.get(ApiUrl.getSystemDetails, data)
  }

  /**
   * 统一订阅\断开操作
   * @param data 
   * @returns 
   */
  getSubscribe = (data: any) => request.get(ApiUrl.getSubscribe, data)

  /**
   * 获取主题统计数据
   * @param data 
   * @returns 
   */
  getTopicStatistic = (data: any) => request.get(ApiUrl.getTopicStatistic, {}, { params: data })
}
const bussinessApi = new BussinessApi()
export default bussinessApi
