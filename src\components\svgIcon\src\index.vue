<script setup>
import { computed, defineProps } from 'vue';
// 引入svg图标
import folderOpen from '@/assets/images/folderOpen.svg';
import folderClose from '@/assets/images/folderClose.svg';
import refresh from '@/assets/images/refresh.svg';
import search from '@/assets/images/search.svg';
import importFile from '@/assets/images/importFile.svg';
import folder from '@/assets/images/folder.svg';
import modelIcon from '@/assets/images/modelIcon.svg';
import modelIconSmall from '@/assets/images/modelIcon_small.svg';
import plus from '@/assets/images/plus.svg';

const svgIconMap = {
  folderOpen, folderClose, refresh, search, importFile, folder, modelIcon, modelIconSmall, plus
}

const props = defineProps({
  name: {
    type: String,
    default: undefined
  },
  className: {
    type: String,
    default: undefined
  }
})

const svgClass = computed(() => {
  if (props.className) {
    return `svg-icon ${props.className}`
  }
  return `svg-icon`

})
</script>

<template>
  <component :class="svgClass" :is="svgIconMap[name]"></component>
</template>

<style lang="less" scoped>
.svg-icon {
  overflow: hidden;
  vertical-align: -0.1em;
}
</style>