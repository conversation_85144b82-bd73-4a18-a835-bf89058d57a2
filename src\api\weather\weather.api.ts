import request from '@/common/request'
import { WeatherApiUrl } from './weather.url'

class WeatherApi {
  /**
   * 查询天气数据
   * @returns 
   */
  getDataList = (data:any) => request.post(WeatherApiUrl.dataList,data)

  /**
   * 新增天气数据
   * @param data 
   * @returns 
   */
  add = (data:any) => request.post(WeatherApiUrl.saveWeatherData, data)

  /**
   * 删除天气数据
   * @param id 
   * @returns 
   */
  deleteData = (id:any) => request.delete(WeatherApiUrl.deleteData.replace('{id}', id))


  /**
   * 更新天气数据
   * @param id 
   * @returns 
   */
  updateData = (id:any,data:any) => request.put(WeatherApiUrl.updateData.replace('{id}', id),data)

}

const weatherApi = new WeatherApi()
export default weatherApi
