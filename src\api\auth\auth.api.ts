import request from '@/common/request'
import { ApiUrl } from './auth.url'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'
// import { axiosIns } from '@/common/ajax'

import ENV from '@/config'

const adminApi: string = ENV.adminApi

class LoginApi {
  login = (data: any) =>
    request.post(ApiUrl.login, data)

  loginV2 = (params: any) => request.get(ApiUrl.loginV2, params)
}
const loginApi = new LoginApi()
export default loginApi
