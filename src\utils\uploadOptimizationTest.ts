/**
 * 文件上传优化测试工具
 * 用于验证分片大小和MD5计算优化效果
 */

import { getOptimizedConfig, formatFileSize, estimateUploadTime } from '@/config/uploadConfig';
import { MD5Calculator } from './md5Calculator';

export interface TestResult {
  fileSize: string;
  chunkSize: string;
  md5ChunkSize: string;
  totalChunks: number;
  md5Chunks: number;
  simultaneousUploads: number;
  estimatedTime: string;
  optimizationLevel: string;
}

/**
 * 测试不同文件大小的优化配置
 */
export function testOptimizationConfigs(): TestResult[] {
  const testSizes = [
    100 * 1024 * 1024,        // 100MB
    500 * 1024 * 1024,        // 500MB
    1024 * 1024 * 1024,       // 1GB
    5 * 1024 * 1024 * 1024,   // 5GB
    10 * 1024 * 1024 * 1024,  // 10GB
    20 * 1024 * 1024 * 1024,  // 20GB
    50 * 1024 * 1024 * 1024,  // 50GB
    100 * 1024 * 1024 * 1024, // 100GB
  ];

  const results: TestResult[] = [];

  testSizes.forEach(size => {
    const config = getOptimizedConfig(size);
    const totalChunks = Math.ceil(size / config.chunkSize);
    const md5Chunks = Math.ceil(size / config.md5ChunkSize);
    
    let optimizationLevel = '标准';
    if (size >= 50 * 1024 * 1024 * 1024) {
      optimizationLevel = '超大文件';
    } else if (size >= 10 * 1024 * 1024 * 1024) {
      optimizationLevel = '大文件';
    } else if (size >= 1024 * 1024 * 1024) {
      optimizationLevel = '中等文件';
    }

    results.push({
      fileSize: formatFileSize(size),
      chunkSize: formatFileSize(config.chunkSize),
      md5ChunkSize: formatFileSize(config.md5ChunkSize),
      totalChunks,
      md5Chunks,
      simultaneousUploads: config.simultaneousUploads,
      estimatedTime: estimateUploadTime(size, 50 * 1024 * 1024), // 假设50MB/s网速
      optimizationLevel
    });
  });

  return results;
}

/**
 * 打印优化配置测试结果
 */
export function printOptimizationTest(): void {
  console.log('🔄 文件上传优化配置测试结果：');
  console.log('=====================================');
  
  const results = testOptimizationConfigs();
  
  results.forEach(result => {
    console.log(`📁 文件大小: ${result.fileSize}`);
    console.log(`   优化级别: ${result.optimizationLevel}`);
    console.log(`   上传分片: ${result.chunkSize} (共${result.totalChunks}片)`);
    console.log(`   MD5分片: ${result.md5ChunkSize} (共${result.md5Chunks}片)`);
    console.log(`   并发数: ${result.simultaneousUploads}`);
    console.log(`   预计耗时: ${result.estimatedTime}`);
    console.log('   -----------------------------------');
  });
  
  console.log('✅ 优化要点：');
  console.log('   • 大文件使用更大分片，减少网络开销');
  console.log('   • MD5计算使用优化算法，减少阻塞');
  console.log('   • 适度并发，平衡速度和稳定性');
  console.log('   • 针对无网络环境优化，避免外部依赖');
}

/**
 * 模拟MD5计算性能测试
 */
export async function testMD5Performance(testFileSize: number = 1024 * 1024 * 1024): Promise<void> {
  console.log('🔄 开始MD5计算性能测试...');
  console.log(`📁 测试文件大小: ${formatFileSize(testFileSize)}`);
  
  // 创建模拟文件
  const mockFile = new File([new ArrayBuffer(testFileSize)], 'test-file.bin');
  
  const config = getOptimizedConfig(testFileSize);
  const calculator = new MD5Calculator();
  
  const startTime = Date.now();
  
  try {
    const result = await calculator.calculateMD5({
      file: mockFile,
      chunkSize: config.md5ChunkSize,
      useWebWorker: false, // 测试主线程性能
      onProgress: (progress) => {
        if (progress % 20 === 0) { // 每20%显示一次
          console.log(`   MD5计算进度: ${progress}%`);
        }
      }
    });
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    const speed = (testFileSize / 1024 / 1024) / duration; // MB/s
    
    console.log('✅ MD5计算性能测试完成：');
    console.log(`   MD5值: ${result.md5}`);
    console.log(`   耗时: ${duration.toFixed(2)}秒`);
    console.log(`   速度: ${speed.toFixed(2)} MB/s`);
    console.log(`   分片大小: ${formatFileSize(config.md5ChunkSize)}`);
    
  } catch (error) {
    console.error('❌ MD5计算测试失败:', error);
  }
}

/**
 * 比较优化前后的效果
 */
export function compareOptimization(): void {
  console.log('🔄 优化前后对比分析：');
  console.log('=====================================');
  
  const testSize = 10 * 1024 * 1024 * 1024; // 10GB文件
  
  // 优化前配置（原始10MB分片）
  const oldConfig = {
    chunkSize: 10 * 1024 * 1024,
    md5ChunkSize: 5 * 1024 * 1024,
    simultaneousUploads: 6
  };
  
  // 优化后配置
  const newConfig = getOptimizedConfig(testSize);
  
  const oldChunks = Math.ceil(testSize / oldConfig.chunkSize);
  const newChunks = Math.ceil(testSize / newConfig.chunkSize);
  
  const oldMd5Chunks = Math.ceil(testSize / oldConfig.md5ChunkSize);
  const newMd5Chunks = Math.ceil(testSize / newConfig.md5ChunkSize);
  
  console.log(`📁 测试文件: ${formatFileSize(testSize)}`);
  console.log('');
  console.log('📊 优化前：');
  console.log(`   上传分片: ${formatFileSize(oldConfig.chunkSize)} × ${oldChunks} = ${oldChunks}片`);
  console.log(`   MD5分片: ${formatFileSize(oldConfig.md5ChunkSize)} × ${oldMd5Chunks} = ${oldMd5Chunks}片`);
  console.log(`   并发数: ${oldConfig.simultaneousUploads}`);
  console.log('');
  console.log('📊 优化后：');
  console.log(`   上传分片: ${formatFileSize(newConfig.chunkSize)} × ${newChunks} = ${newChunks}片`);
  console.log(`   MD5分片: ${formatFileSize(newConfig.md5ChunkSize)} × ${newMd5Chunks} = ${newMd5Chunks}片`);
  console.log(`   并发数: ${newConfig.simultaneousUploads}`);
  console.log('');
  console.log('🎯 优化效果：');
  console.log(`   上传分片数减少: ${((oldChunks - newChunks) / oldChunks * 100).toFixed(1)}%`);
  console.log(`   MD5分片数减少: ${((oldMd5Chunks - newMd5Chunks) / oldMd5Chunks * 100).toFixed(1)}%`);
  console.log(`   网络请求减少: ${oldChunks - newChunks}次`);
  console.log(`   MD5计算优化: ${oldMd5Chunks - newMd5Chunks}次循环减少`);
}