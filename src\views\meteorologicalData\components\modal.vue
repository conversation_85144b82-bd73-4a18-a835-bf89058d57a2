<script setup>
    import { ref, defineProps, defineEmits, watch } from 'vue';
    import weatherApi from '@/api/weather/weather.api'
    import { Modal, message } from 'ant-design-vue';
    import dayjs from 'dayjs';
    import {
        weather,
        windDirection
    } from '../common/selectOptions.enum.ts'
    const emits = defineEmits(['close', 'handleOk'])
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'add'
        },
        row: {
            type: Object,
            default: {}
        }
    });

    const formRef = ref();
    const loading = ref(false)
    const formState = ref({
        location: '',
        observationDate: '',
        weatherPhenomenon: '',
        windDirection: '',
        temperature: '',
        humidity: '',
        pressure: '',
        observer: '',
        windSpeed: ''
    })


    const handleOk = (e) => {
        console.log(e);
        if (props.type === 'check') {
            cancel()
        } else {
            loading.value = true;
            formRef.value.validate().then(() => {
                if (props.type == 'add') {
                    console.log('新增数据:', formState.value)
                    weatherApi.add(formState.value).then(res => {
                        if (res?.data?.id) {
                            message.success("保存成功！");
                            loading.value = false;
                            emits('handleOk');
                            formRef.value.resetFields();
                            formState.value = {
                                location: '',
                                observationDate: '',
                                weatherPhenomenon: '',
                                windDirection: '',
                                temperature: '',
                                humidity: '',
                                pressure: '',
                                observer: '',
                                windSpeed: ''
                            }
                        }
                    }).catch(error => {
                        console.log('error');
                        loading.value = false;
                    })
                } else {
                    console.log('修改数据:', formState.value)
                    // 调用修改接口
                    loading.value = false;
                    weatherApi.updateData(formState.value.id, formState.value).then(res => {
                        if (res?.data?.id) {
                            message.success("修改成功！");
                            loading.value = false;
                            emits('handleOk');
                            formRef.value.resetFields();
                        }
                    }).catch(error => {
                        console.log('error');
                        loading.value = false;
                    })

                }
            }).catch(error => {
                console.log('error');
                loading.value = false;
            })
        }
    };

    const cancel = () => {
        emits('close')
        formRef.value.resetFields()
        formState.value = {
            name: undefined,
            code: undefined,
            description: undefined
        }
    }

    watch(() => props.visible, (newVal, oldVal) => {
        if (newVal) {
            formState.value = props.row
            if (formState.value.observationDate) formState.value.observationDate = dayjs(formState.value.observationDate)
        }
    })
</script>
<template>
    <a-modal v-model:open="props.visible" wrapClassName="middle-modal"
        :title="props.type === 'add' ? '新增' : props.type === 'modify' ? '修改' : '查看'" @ok="handleOk" @cancel="cancel"
        :cancel-button-props="{
            style: {
                display: props.type === 'check' ? 'none' : 'inline-block'
            }
        }" :loading="loading">
        <a-form ref="formRef" :model="formState" name="basic" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }"
            autocomplete="off">
            <a-form-item label="观察场位置" name="location" :rules="[{ required: true, message: '请输入观察场位置' }]">
                <a-input v-model:value="formState.location" placeholder="请输入" :disabled="props.type === 'check'" />
            </a-form-item>
            <a-form-item label="观察日期" name="observationDate" :rules="[{ required: true, message: '请选择观察日期' }]">
                <a-date-picker style="width: 100%;" v-model:value="formState.observationDate" placeholder="请选择"
                    :disabled="props.type === 'check'" valueFormat="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item label="天气现象" name="weatherPhenomenon" :rules="[{ required: true, message: '请选择' }]">
                <a-select :options="weather" v-model:value="formState.weatherPhenomenon" placeholder="请选择"
                    :disabled="props.type === 'check'" />
            </a-form-item>
            <a-form-item label="风向" name="windDirection" :rules="[{ required: true, message: '请选择风向' }]">
                <a-select :options="windDirection" v-model:value="formState.windDirection" placeholder="请选择"
                    :disabled="props.type === 'check'" />
            </a-form-item>
            <a-form-item label="风速（m/s）" name="windSpeed" :rules="[{ required: true, message: '请输入风速' }]">
                <a-input type="number" v-model:value="formState.windSpeed" placeholder="请输入"
                    :disabled="props.type === 'check'" />
            </a-form-item>
            <a-form-item label="温度（℃）" name="temperature" :rules="[{ required: true, message: '请输入温度' }]">
                <a-input type="number" v-model:value="formState.temperature" placeholder="请输入"
                    :disabled="props.type === 'check'" />
            </a-form-item>
            <a-form-item label="湿度（%）" name="humidity" :rules="[{ required: true, message: '请输入湿度' }]">
                <a-input type="number" v-model:value="formState.humidity" placeholder="请输入"
                    :disabled="props.type === 'check'" />
            </a-form-item>

            <a-form-item label="气压（hPa）" name="pressure" :rules="[{ required: true, message: '请输入气压' }]">
                <a-input type="number" v-model:value="formState.pressure" placeholder="请输入"
                    :disabled="props.type === 'check'" />
            </a-form-item>
            <a-form-item label="观察员" name="observer" :rules="[{ required: true, message: '请输入观察员' }]">
                <a-input v-model:value="formState.observer" placeholder="请输入" :disabled="props.type === 'check'" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button key="back" @click="cancel">取消</a-button>
            <a-button key="submit" type="primary" :loading="loading" @click="handleOk">确定</a-button>
        </template>
    </a-modal>
</template>