import request from '@/common/request'
import { ApiUrl } from './data.url'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'
// import { axiosIns } from '@/common/ajax'

import ENV from '@/config'

const adminApi: string = ENV.adminApi

class DataApi {
  getSSBWPageTable = (data: any) =>
    request.post(ApiUrl.getSSBWPageTable, data)
  downloadQuery = (data: any) =>
    request.post(ApiUrl.downloadQuery, data, { responseType: 'blob' })
}
const dataApi = new DataApi()
export default dataApi
