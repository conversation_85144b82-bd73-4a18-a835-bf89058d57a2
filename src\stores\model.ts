import { ref, computed, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useModelStore = defineStore('modelState', () => {
  
  const modelState:any = reactive({
    curSelectFolder: undefined, // 模型选择
    curSelectData: undefined, // 数据选择
  })

  function updateSelectFolder(folder: any) {
    modelState.curSelectFolder = folder
  }

  function updateSelectData(data: any) {
    modelState.curSelectData = data
  }

  return { modelState, updateSelectFolder, updateSelectData }
})

