/**
 * 手动文件上传工具类，用于断点续传
 */
import fileApi from '@/api/file/file.api';
import { getStepFromPath, validateStep, cleanupCompletedFile } from '@/config/uploadConfig';

interface UploadParams {
  filePath?: string;
  filename: string;
  chunkNumber: number;
  chunkSize: number;
  relativePath: string; // 恢复 relativePath 参数
  totalChunks: number;
  totalSize: number;
  currentChunkSize: number;
  identifier: string;
  file: Blob;
}

interface UploadProgress {
  identifier: string;
  uploaded: number;
  total: number;
  percentage: number;
}

interface TaskInfo {
  identifier: string;
  taskId?: number;
}

class ManualUploader {
  private chunkSize = 32 * 1024 * 1024; // 32MB per chunk，大文件优化
  private maxConcurrentUploads = 6; // 最大并发上传数，大文件优化
  private onProgress?: (progress: UploadProgress) => void;
  private onComplete?: (identifier: string) => void;
  private onError?: (error: Error, identifier: string) => void;
  private taskInfoMap: Map<string, TaskInfo> = new Map(); // 存储文件标识符和任务信息的映射
  private pausedUploads: Set<string> = new Set(); // 存储暂停的上传标识符
  private uploadingFiles: Set<string> = new Set(); // 存储正在上传的文件标识符，防止在上传过程中被暂停
  private instanceId: string = Math.random().toString(36).substr(2, 9); // 实例唯一标识
  private activeUploads: Map<string, number> = new Map(); // 存储每个文件的活跃上传数

  constructor(options?: {
    chunkSize?: number;
    maxConcurrentUploads?: number;
    onProgress?: (progress: UploadProgress) => void;
    onComplete?: (identifier: string) => void;
    onError?: (error: Error, identifier: string) => void;
  }) {
    if (options?.chunkSize) {
      this.chunkSize = options.chunkSize;
    }
    if (options?.maxConcurrentUploads) {
      this.maxConcurrentUploads = options.maxConcurrentUploads;
    }
    this.onProgress = options?.onProgress;
    this.onComplete = options?.onComplete;
    this.onError = options?.onError;
    
    // 确保新实例没有任何暂停状态
    this.pausedUploads.clear();
    console.log(`🆕 创建新的手动上传器实例 [${this.instanceId}]，暂停状态已清空`);
  }

  /**
   * 设置任务信息
   * @param identifier 文件标识符
   * @param taskId 任务ID
   */
  setTaskInfo(identifier: string, taskId?: number): void {
    this.taskInfoMap.set(identifier, { identifier, taskId });
  }

  /**
   * 获取任务信息
   * @param identifier 文件标识符
   */
  getTaskInfo(identifier: string): TaskInfo | undefined {
    return this.taskInfoMap.get(identifier);
  }

  /**
   * 暂停上传
   * @param identifier 文件标识符
   * @param force 是否强制暂停，即使文件正在上传中
   */
  pauseUpload(identifier: string, force: boolean = false): void {
    console.log(`🔴 [${this.instanceId}] pauseUpload 被调用 - 文件: ${identifier}, 强制: ${force}`);
    console.log(`🔴 [${this.instanceId}] 调用栈:`, new Error().stack?.split('\n').slice(1, 4).join('\n'));
    
    // 如果文件正在上传中且不是强制暂停，不允许暂停，避免竞态条件
    if (this.uploadingFiles.has(identifier) && !force) {
      console.warn(`⚠️ [${this.instanceId}] 文件 ${identifier} 正在上传中，暂停操作被忽略以避免竞态条件。如需强制暂停，请使用 force 参数`);
      return;
    }
    this.pausedUploads.add(identifier);
    console.log(`🔴 [${this.instanceId}] 上传已暂停: ${identifier}${force ? ' (强制暂停)' : ''}, 暂停列表:`, Array.from(this.pausedUploads));
  }

  /**
   * 恢复上传
   * @param identifier 文件标识符
   */
  resumeUpload(identifier: string): void {
    const wasDeleted = this.pausedUploads.delete(identifier);
    console.log(`🟢 [${this.instanceId}] resumeUpload - 文件: ${identifier}, 删除成功: ${wasDeleted}, 暂停列表:`, Array.from(this.pausedUploads));
  }

  /**
   * 清理所有暂停状态
   */
  clearAllPausedStates(): void {
    const sizeBefore = this.pausedUploads.size;
    this.pausedUploads.clear();
    console.log(`🟡 [${this.instanceId}] clearAllPausedStates - 清理前大小: ${sizeBefore}, 清理后大小: ${this.pausedUploads.size}`);
  }

  /**
   * 强制重置上传状态，确保能够正常开始上传
   */
  forceResetUploadState(identifier: string): void {
    this.pausedUploads.delete(identifier);
    // 不要删除 uploadingFiles 标记，因为文件正在上传
    console.log(`🔧 [${this.instanceId}] 强制重置文件 ${identifier} 的暂停状态`);
  }

  /**
   * 完全重置所有状态，用于创建全新实例
   */
  resetAllStates(): void {
    this.pausedUploads.clear();
    // 不清理 uploadingFiles，因为可能有文件正在上传
    this.taskInfoMap.clear();
    console.log(`🔄 [${this.instanceId}] 已重置暂停状态和任务映射`);
  }

  /**
   * 检查是否已暂停
   * @param identifier 文件标识符
   */
  isPaused(identifier: string): boolean {
    const paused = this.pausedUploads.has(identifier);
    console.log(`🔍 [${this.instanceId}] 检查暂停状态 - 文件: ${identifier}, 暂停: ${paused}, 暂停列表大小: ${this.pausedUploads.size}, 内容:`, Array.from(this.pausedUploads));
    return paused;
  }

  /**
   * 上传文件
   * @param file 文件对象
   * @param identifier 文件标识符
   * @param uploadedChunks 已上传的分片数组
   * @param filePath 文件路径
   * @param fileName 文件名称
   * @param taskId 任务ID（可选）
   */
  async uploadFile(file: File, identifier: string, uploadedChunks: number[] = [], filePath: string = '', fileName: string = '', taskId?: number): Promise<void> {
    try {
      const totalChunks = Math.ceil(file.size / this.chunkSize);
      let uploadedCount = uploadedChunks.length;

      // 标记文件开始上传，防止在上传过程中被暂停
      this.uploadingFiles.add(identifier);
      
      // 多重清除暂停状态，确保上传能够正常开始
      this.pausedUploads.delete(identifier);
      this.resumeUpload(identifier);
      this.forceResetUploadState(identifier);
      console.log(`🧹 [${this.instanceId}] 多重清除文件 ${identifier} 的暂停状态`);

      // 设置任务信息
      if (taskId) {
        this.setTaskInfo(identifier, taskId);
      }

      console.log(`📤 [${this.instanceId}] 开始上传文件: ${file.name}, 总分片: ${totalChunks}, 已上传: ${uploadedCount}`);
      console.log(`📤 [${this.instanceId}] 上传开始前暂停状态检查 - 文件: ${identifier}, 暂停状态: ${this.isPaused(identifier)}, 暂停列表:`, Array.from(this.pausedUploads));

      // 最终确认暂停状态已清除
      if (this.isPaused(identifier)) {
        console.error(`❌ 检测到暂停状态仍然存在，强制清除`);
        this.pausedUploads.clear(); // 清除所有暂停状态
        console.log(`已强制清除所有暂停状态，重新检查: ${this.isPaused(identifier)}`);
      }

      // 上传剩余的分片
      for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
        // 检查是否已暂停
        if (this.isPaused(identifier)) {
          console.log(`🔴 [${this.instanceId}] 文件 ${identifier} 已暂停，停止上传`);
          throw new Error('上传已暂停');
        }

        // 跳过已上传的分片
        if (uploadedChunks.includes(chunkNumber)) {
          continue;
        }

        const start = (chunkNumber - 1) * this.chunkSize;
        const end = Math.min(start + this.chunkSize, file.size);
        const chunk = file.slice(start, end);

        // 使用传入的 fileName，如果没有则使用 file.name
        const actualFileName = fileName || file.name;

        console.log(`🔄 [${this.instanceId}] 分片 ${chunkNumber} 上传参数:`, {
          filePath: filePath,
          filename: actualFileName,
          identifier: identifier,
          taskId: this.taskInfoMap.get(identifier)?.taskId
        });

        const uploadParams: UploadParams = {
          filePath: filePath, // 直接使用传入的 filePath，不使用默认值
          filename: actualFileName, // 使用正确的文件名
          chunkNumber: chunkNumber,
          chunkSize: this.chunkSize,
          relativePath: actualFileName, // 设置为与 filename 相同
          totalChunks: totalChunks,
          totalSize: file.size,
          currentChunkSize: chunk.size,
          identifier: identifier,
          file: chunk
        };

        try {
          await this.uploadChunk(uploadParams);
          
          // 🔥 重要：将当前分片添加到已上传列表，确保进度计算正确
          if (!uploadedChunks.includes(chunkNumber)) {
            uploadedChunks.push(chunkNumber);
          }
          
          // 🔥 重要：使用实际已上传分片数计算进度，避免进度计算错误
          const actualUploadedCount = uploadedChunks.length;

          // 更新进度
          const progress: UploadProgress = {
            identifier: identifier,
            uploaded: actualUploadedCount,
            total: totalChunks,
            percentage: (actualUploadedCount / totalChunks) * 100
          };

          this.onProgress?.(progress);
          console.log(`分片 ${chunkNumber}/${totalChunks} 上传完成, 进度: ${progress.percentage.toFixed(2)}%, 已上传分片总数: ${actualUploadedCount}`);
          
          // 🔥 重要：如果文件上传完成，立即标记为已完成状态，防止后续进度回退
          if (actualUploadedCount >= totalChunks) {
            console.log(`🎯 所有分片上传完成！总分片: ${totalChunks}, 已上传: ${actualUploadedCount}`);
            this.markFileAsCompleted(identifier);
          }
        } catch (error) {
          console.error(`分片 ${chunkNumber} 上传失败:`, error);
          // 分片上传失败时，抛出错误停止整个上传过程
          throw new Error(`分片 ${chunkNumber} 上传失败: ${(error as Error).message}`);
        }
      }

      console.log(`文件 ${file.name} 上传完成`);

      // 文件上传完成后，标记为已完成状态，防止进度回退
      this.markFileAsCompleted(identifier);
      
      // 文件上传完成后，调用批量设置成功状态接口
      await this.markTaskAsSuccess(identifier);

      // 🔥 重要：文件上传100%完成后，使用统一清理函数清除所有相关数据
      await cleanupCompletedFile(identifier);

      this.onComplete?.(identifier);

    } catch (error) {
      console.error('文件上传失败:', error);
      this.onError?.(error as Error, identifier);
      throw error;
    } finally {
      // 无论成功还是失败，都要清理上传中标记
      this.uploadingFiles.delete(identifier);
      console.log(`文件 ${identifier} 上传结束，清理上传中标记`);
    }
  }

  /**
   * 上传单个分片
   */
  private async uploadChunk(params: UploadParams): Promise<void> {
    const formData = new FormData();

    // 获取当前页面的step参数
    const currentStep = getStepFromPath();
    console.log(`🔄 [${this.instanceId}] 手动上传器使用step参数: ${currentStep}`);

    // 验证step参数
    if (!validateStep(currentStep)) {
        console.error(`🔄 [${this.instanceId}] 无效的step参数: ${currentStep}`);
    }

    // 添加所有必要的参数
    formData.append('filePath', params.filePath || '');
    formData.append('filename', params.filename);
    formData.append('chunkNumber', params.chunkNumber.toString());
    formData.append('chunkSize', params.chunkSize.toString());
    formData.append('relativePath', params.relativePath); // 恢复 relativePath 参数
    formData.append('totalChunks', params.totalChunks.toString());
    formData.append('totalSize', params.totalSize.toString());
    formData.append('currentChunkSize', params.currentChunkSize.toString());
    formData.append('identifier', params.identifier);
    formData.append('step', currentStep); // 添加step参数
    formData.append('file', params.file);

    try {
      const response = await fetch(`${window.location.origin}/filetransfer/uploadfile`, {
        method: 'POST',
        headers: {
          'token': localStorage.getItem('token') || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || '上传失败');
      }

      return result;
    } catch (error) {
      console.error(`分片 ${params.chunkNumber} 上传失败:`, error);
      throw error;
    }
  }

  /**
   * 标记任务为成功状态
   * @param identifier 文件标识符
   */
  private async markTaskAsSuccess(identifier: string): Promise<void> {
    try {
      const taskInfo = this.getTaskInfo(identifier);
      if (taskInfo && taskInfo.taskId) {
        console.log(`调用批量设置成功状态接口，任务ID: ${taskInfo.taskId}`);
        await fileApi.batchSuccess([taskInfo.taskId]);
        console.log(`任务 ${taskInfo.taskId} 状态已设置为成功`);
        
        // 将任务ID添加到已完成列表，防止后续调用其他状态更新接口
        this.addToCompletedTasks(taskInfo.taskId);
        
        // 清理任务信息
        this.taskInfoMap.delete(identifier);
      } else {
        console.warn(`文件 ${identifier} 没有关联的任务ID，跳过状态设置`);
      }
    } catch (error) {
      console.error(`设置任务成功状态失败:`, error);
      // 不抛出错误，避免影响上传流程
    }
  }

  /**
   * 将任务ID添加到已完成列表
   * @param taskId 任务ID
   */
  private addToCompletedTasks(taskId: number): void {
    try {
      const completedTasks = this.getCompletedTasks();
      if (!completedTasks.includes(taskId)) {
        completedTasks.push(taskId);
        localStorage.setItem('completedTaskIds', JSON.stringify(completedTasks));
        console.log(`任务 ${taskId} 已添加到已完成列表`);
      }
    } catch (error) {
      console.error('添加到已完成列表失败:', error);
    }
  }

  /**
   * 获取已完成的任务ID列表
   */
  private getCompletedTasks(): number[] {
    try {
      const completedTasks = localStorage.getItem('completedTaskIds');
      return completedTasks ? JSON.parse(completedTasks) : [];
    } catch (error) {
      console.error('获取已完成任务列表失败:', error);
      return [];
    }
  }
  
  /**
   * 标记文件为已完成状态，防止进度被重置
   */
  private markFileAsCompleted(identifier: string): void {
    try {
      const completedFiles = this.getCompletedFiles();
      if (!completedFiles.includes(identifier)) {
        completedFiles.push(identifier);
        localStorage.setItem('completedFileIdentifiers', JSON.stringify(completedFiles));
        console.log(`文件 ${identifier} 已标记为完成状态，防止进度重置`);
      }
    } catch (error) {
      console.error('标记文件完成状态失败:', error);
    }
  }

  /**
   * 获取已完成的文件标识符列表
   */
  private getCompletedFiles(): string[] {
    try {
      const completedFiles = localStorage.getItem('completedFileIdentifiers');
      return completedFiles ? JSON.parse(completedFiles) : [];
    } catch (error) {
      console.error('获取已完成文件列表失败:', error);
      return [];
    }
  }



}

export default ManualUploader;
export type { UploadProgress };