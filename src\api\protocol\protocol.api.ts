import request from '@/common/request'
import { ApiUrl } from './protocol.url'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'
// import { axiosIns } from '@/common/ajax'

import ENV from '@/config'

const adminApi: string = ENV.adminApi

class ProtocolApi {
  /**
 * 根据系统id获取protocol列表
 * @param id 系统id
 * @returns 
 */
  getProtocolListBySystem = (systemId: any) => {
    return request.get(ApiUrl.getProtocolListBySystem.replace('{systemId}', systemId))
  }
  getProtocolList = (data: any) => {
    return request.get(ApiUrl.getProtocolList, { params: data })
  }
  getProtocolPage = (data: any, body) => {
    return request.post(ApiUrl.getProtocolPage, body, { params: data })
  }
  subscribeProtocol = (data: any) => {
    console.log('datadatadata', data);

    return request.post(ApiUrl.subscribeProtocol, data)
  }
}
const protocolApi = new ProtocolApi()
export default protocolApi
