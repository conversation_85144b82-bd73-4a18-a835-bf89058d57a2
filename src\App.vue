<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import zhCN from 'ant-design-vue/es/locale/zh_CN';
const systemTheme = {
  token: {
  },
  components: {
    Input: {
      borderRadius: '4px',
      colorBorder: '#3a3d51',
      colorPrimaryHover: '#1890FF',
      colorBgContainer: '#10121a',
    },
    Table: {
      colorBorderSecondary: '#06122A',
      colorBgContainer: '#06122A',
      borderRadiusLG: 4,
      fontSize: 12,
      lineHeight: 1
    },
    Dropdown: {
      colorBgElevated: '#2c2f40',
      borderRadiusLG: 0,
      borderRadius: '0px',
      colorPrimary: '#1890FF',
      colorPrimaryHover: '#1890FF',
      colorBgElevated: '#1e202d',
      controlItemBgActive: '#3a3d51',
      controlItemBgHover: '#3a3d51',
    },
  }
  }
</script>

<template>
  <a-config-provider :locale="zhCN" :theme="systemTheme">
    <RouterView />
  </a-config-provider>
</template>

<style lang="less" scoped></style>
<style>
</style>
