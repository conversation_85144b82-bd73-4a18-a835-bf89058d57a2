<script setup>
import { ref, defineProps, defineEmits, onMounted, onUnmounted, createVNode, reactive, watch } from 'vue';
import {
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import fileApi from '@/api/file/file.api'
import { useUploadStore } from '@/stores/upload';
import emitter from '@/common/eventBus';
import indexedDBUtil from '@/utils/indexedDB';
import ManualUploader from '@/utils/manualUploader';
import { cleanupCompletedFile } from '@/config/uploadConfig';

const uploadStore = useUploadStore()
const emits = defineEmits(['close'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});
const typeList = [
  { label: '上传', key: 'up' },
  { label: '下载', key: 'down' },
]
const statusList = [
  { label: '进行中', key: 0, num: 0 },
  { label: '已完成', key: 1, num: 0 },
  { label: '已取消', key: 3, num: 0 },
]
const curTypeKey = ref('up')
const activeTabKey = ref(0)
const manualUploaders = ref(new Map()) // 存储正在使用的手动上传器实例
const operationInProgress = ref(new Set()) // 正在进行操作的文件标识符集合，防止重复操作
const refreshTimer = ref(null) // 防抖定时器
const tableState = reactive({
  list: [],
  loading: false,
  pagination: {
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
  },
  selectedRowKeys: [],
  columns: [{
    title: '文件名称',
    dataIndex: 'fileName',
    key: 'fileName',
    width: 200,
    ellipsis: true
  },
  {
    title: '文件大小',
    dataIndex: 'fileSizeFormatted',
    key: 'fileSizeFormatted',
    width: 100,
    align: 'center'
  },
  {
    title: '发起时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
    ellipsis: true
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 160,
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 120,
    fixed: 'right',
    align: 'center'
  },]
})

function onChangeType(data) {
  curTypeKey.value = data.key
}

async function onChangeTab(data) {
  activeTabKey.value = data.key
  tableState.pagination.current = 1
  tableState.list = []
  
  // 获取任务列表
  await getTaskList()
  
  // 当切换到"进行中"标签时，恢复断点续传
  if (activeTabKey.value === 0) {
    await restoreUploadProgress()
  }
}

const cancel = () => {
  curTypeKey.value = 'up'
  activeTabKey.value = '1'
  emits('close')
}

function onSelectChange(selectedRowKeys) {
  tableState.selectedRowKeys = selectedRowKeys;
}

function getTaskList() {
  const params = {
    pagingSort: {
      currentPage: tableState.pagination.current,
      pageSize: tableState.pagination.pageSize
    },
    sortList: [],
    taskStatus: activeTabKey.value
  }
  tableState.loading = true
  
  return new Promise((resolve, reject) => {
    fileApi.listByBatchId(params).then(res => {
      const { records, total } = res.data.resultPage
      tableState.list = records
      tableState.pagination.total = total
      const { CANCEL, COMPLETE, UPLOADING } = res.data.statusStatistics
      statusList[0].num = UPLOADING
      statusList[1].num = COMPLETE
      statusList[2].num = CANCEL
      
      // 更新文件标识符和任务ID的映射关系
      updateFileTaskMapping(records);
      
      resolve(records);
    }).catch(error => {
      console.error('获取任务列表失败:', error);
      reject(error);
    }).finally(() => {
      tableState.loading = false
    });
  });
}

/**
 * 更新文件标识符和任务ID的映射关系
 */
function updateFileTaskMapping(records) {
  try {
    // 从 localStorage 获取现有映射
    let mappingObj = {};
    const existingMapping = localStorage.getItem('fileTaskMapping');
    
    if (existingMapping) {
      mappingObj = JSON.parse(existingMapping);
    }
    
    // 更新映射
    records.forEach(record => {
      if (record.identifier && record.id) {
        mappingObj[record.identifier] = record.id;
      }
    });
    
    // 保存回 localStorage
    localStorage.setItem('fileTaskMapping', JSON.stringify(mappingObj));
    console.log('文件任务映射已更新:', mappingObj);
    
    // 如果是"进行中"的任务列表，保存所有任务ID和状态
    if (activeTabKey.value === 0) {
      // 保存进行中的任务ID
      const inProgressTaskIds = records
        .filter(record => record.taskStatus === 0) // 只保存状态为0（上传中）的任务
        .map(record => record.id)
        .filter(Boolean);
      
      // 更新到全局状态
      uploadStore.updateInProgressTaskIds(inProgressTaskIds);
      console.log('进行中的任务已更新:', inProgressTaskIds);
      
      // 不再需要同步uploadingFiles状态，直接使用taskStatus判断按钮状态
      console.log('任务列表已更新，按钮状态将根据taskStatus自动显示');
      
      // 检查是否有暂停状态的任务（taskStatus === 2）
      const pausedTasks = records.filter(record => record.taskStatus === 2);
      console.log('暂停状态的任务:', pausedTasks);
    }
  } catch (error) {
    console.error('更新文件任务映射失败:', error);
  }
}

function onCancelTask(data) {
  let ids = []
  let uniqueIdentifiers = []
  if (data) {
    ids.push(data.id)
    uniqueIdentifiers.push(data.identifier)
  } else {
    // 获取选中的行
    for (const item of tableState.list) {
      if (tableState.selectedRowKeys.includes(item.id)) {
        ids.push(item.id)
        if (item.identifier) {
          uniqueIdentifiers.push(item.identifier)
        }
      }
    }
  }

  // 取消上传器实例（如果存在）
  for (const identifier of uniqueIdentifiers) {
    console.log(`取消文件: ${identifier}`);

    // 检查是否有手动上传器实例
    const manualUploader = manualUploaders.value.get(identifier);
    if (manualUploader) {
      console.log(`取消手动上传器: ${identifier}`);
      manualUploaders.value.delete(identifier);
    } else {
      console.log(`取消原生上传器: ${identifier}`);
    }

    // 清理进度和IndexedDB
    uploadStore.clearProgress(identifier);
    indexedDBUtil.deleteFile(identifier).then(() => {
      console.log(`已从 IndexedDB 删除取消的文件 ${identifier}`);
    }).catch(error => {
      console.error(`删除 IndexedDB 文件失败:`, error);
    });
  }

  // 通知上传组件取消上传
  uploadStore.updateCancelList(uniqueIdentifiers)

  // 调用后端接口修改任务状态为取消
  if (ids.length > 0) {
    fileApi.batchCancel(ids).then(async res => {
      console.log('任务取消状态更新成功');
      try {
        await getTaskList() // 刷新任务列表
      } catch (error) {
        console.error('刷新任务列表失败:', error)
      }
    }).catch(error => {
      console.error('取消任务失败:', error)
      message.error('取消任务失败')
    })
  } else {
    message.warning('未选择任何任务')
  }
}

function onDeleteTask(data) {
  let ids = []
  let uniqueIdentifiers = []
  if (data) {
    ids.push(data.id)
    if (data.identifier) {
      uniqueIdentifiers.push(data.identifier)
    }
  } else {
    // 获取选中的行
    for (const item of tableState.list) {
      if (tableState.selectedRowKeys.includes(item.id)) {
        ids.push(item.id)
        if (item.identifier) {
          uniqueIdentifiers.push(item.identifier)
        }
      }
    }
  }

  // 清理删除任务的相关数据
  for (const identifier of uniqueIdentifiers) {
    if (identifier) {
      // 清理手动上传器实例
      manualUploaders.value.delete(identifier);
      // 清理进度
      uploadStore.clearProgress(identifier);
      // 从 IndexedDB 删除文件
      indexedDBUtil.deleteFile(identifier).then(() => {
        console.log(`已从 IndexedDB 删除文件 ${identifier}`);
      }).catch(error => {
        console.error(`删除 IndexedDB 文件失败:`, error);
      });
    }
  }

  // 调用后端接口删除任务
  if (ids.length > 0) {
    fileApi.batchDelete(ids).then(async res => {
      await getTaskList()
    }).catch(error => {
      console.error('删除任务失败:', error)
      message.error('删除任务失败')
    })
  } else {
    message.warning('未选择任何任务')
  }
}

function onPauselTask(data) {
  console.log('🔴 暂停按钮被点击，data:', data);

  let ids = []
  let uniqueIdentifiers = []
  if (data) {
    console.log('🔴 单个任务暂停，任务ID:', data.id, '标识符:', data.identifier);
    ids.push(data.id)
    uniqueIdentifiers.push(data.identifier)
  } else {
    // 获取选中的行
    for (const item of tableState.list) {
      if (tableState.selectedRowKeys.includes(item.id)) {
        ids.push(item.id)
        if (item.identifier) {
          uniqueIdentifiers.push(item.identifier)
        }
      }
    }
  }

  // 清除浏览器关闭标记，因为这是正常的手动暂停操作
  localStorage.removeItem('pausedByClose');

  // 暂停上传器实例（如果存在）
  console.log(`🔄 开始暂停上传器实例，当前页面: ${window.location.pathname}`);
  for (const identifier of uniqueIdentifiers) {
    console.log(`🔄 暂停文件: ${identifier}`);

    // 检查是否有手动上传器实例
    const manualUploader = manualUploaders.value.get(identifier);
    if (manualUploader) {
      console.log(`🔄 暂停手动上传器: ${identifier}`);
      manualUploader.pauseUpload(identifier, true);
    } else {
      console.log(`🔄 暂停原生上传器: ${identifier}`);
      // 通知原生上传组件暂停
      emitter.emit('pause.upload', { uniqueIdentifiers: [identifier] });
    }
  }

  // 调用后端接口修改任务状态为暂停
  console.log('🔴 准备调用暂停接口，任务IDs:', ids);
  if (ids.length > 0) {
    console.log('🔴 开始调用 fileApi.batchPause 接口');
    fileApi.batchPause(ids).then(async res => {
      console.log('🔴 暂停接口调用成功，响应:', res);
      console.log('任务暂停状态更新成功');
      try {
        console.log('🔴 开始刷新任务列表');
        await getTaskList() // 刷新任务列表，按钮状态会根据taskStatus自动更新
        console.log('🔴 任务列表刷新完成');
      } catch (error) {
        console.error('刷新任务列表失败:', error)
      }
    }).catch(error => {
      console.error('🔴 暂停任务失败:', error)
      message.error('暂停任务失败')
    })
  } else {
    console.log('🔴 没有任务ID，显示警告');
    message.warning('未选择任何任务')
  }
}

async function onResumelTask(data) {
  let ids = []
  let uniqueIdentifiers = []

  // 检查是否是浏览器关闭后重新打开的情况（在清除标记前保存状态）
  const pausedByClose = localStorage.getItem('pausedByClose');
  const wasPausedByClose = pausedByClose === 'true';

  // 清除浏览器关闭标记，因为用户主动点击了继续
  if (wasPausedByClose) {
    localStorage.removeItem('pausedByClose');
    console.log('🔄 用户主动点击继续，已清除浏览器关闭标记');
  }

  if (data) {
    // 单个任务恢复
    if (data.taskStatus !== 1) { // 只有非完成状态的任务才能恢复
      ids.push(data.id)
      uniqueIdentifiers.push(data.identifier)
    } else {
      message.warning('该任务已完成，无需恢复')
      return
    }
  } else {
    // 批量恢复选中的行
    for (const item of tableState.list) {
      if (tableState.selectedRowKeys.includes(item.id)) {
        if (item.taskStatus !== 1) { // 只有非完成状态的任务才能恢复
          ids.push(item.id)
          if (item.identifier) {
            uniqueIdentifiers.push(item.identifier)
          }
        }
      }
    }

    if (ids.length === 0) {
      message.warning('所选任务均已完成，无需恢复')
      return
    }
  }

  // 使用之前获取的pausedByClose变量

  // 恢复上传器实例
  console.log(`🔄 开始恢复上传器实例，当前页面: ${window.location.pathname}`);
  console.log(`🔄 文件标识符列表:`, uniqueIdentifiers);
  console.log(`🔄 wasPausedByClose 状态:`, wasPausedByClose);

  for (const identifier of uniqueIdentifiers) {
    console.log(`🔄 处理文件: ${identifier}`);

    // 检查是否有已暂停的手动上传器实例
    const existingUploader = manualUploaders.value.get(identifier);
    console.log(`🔄 文件 ${identifier} 的现有上传器:`, existingUploader ? '存在' : '不存在');

    if (existingUploader && existingUploader.isPaused(identifier)) {
      console.log(`🔄 恢复已暂停的手动上传器: ${identifier}`);
      existingUploader.resumeUpload(identifier);
      // 继续上传逻辑
      resumeUploadWithManualUploader(identifier);
    } else if (wasPausedByClose) {
      console.log(`🔄 浏览器关闭后重新打开，使用手动上传器恢复: ${identifier}`);
      console.log(`🔄 将调用 resumeUploadWithManualUploader 函数，该函数会调用 filetransfer/uploadfile 接口`);
      // 浏览器关闭后重新打开，使用手动上传器恢复上传
      resumeUploadWithManualUploader(identifier);
    } else {
      console.log(`🔄 跨页面恢复上传，使用手动上传器: ${identifier}`);
      // 跨页面恢复时，统一使用手动上传器，确保在任何页面都能正常工作
      resumeUploadWithManualUploader(identifier);
    }
  }

  // 调用后端接口修改任务状态为上传中
  if (ids.length > 0) {
    fileApi.batchUpload(ids).then(async res => {
      console.log('任务恢复状态更新成功');
      try {
        await getTaskList() // 刷新任务列表，按钮状态会根据taskStatus自动更新
      } catch (error) {
        console.error('刷新任务列表失败:', error)
      }
    }).catch(error => {
      console.error('恢复任务失败:', error)
      message.error('恢复任务失败')
    })
  } else {
    message.warning('未选择任何任务')
  }
}

/**
 * 使用手动上传器恢复上传（简化版本）
 */
async function resumeUploadWithManualUploader(identifier) {
  console.log(`🔄 resumeUploadWithManualUploader 被调用，文件标识符: ${identifier}`);

  // 检查是否正在进行操作
  if (operationInProgress.value.has(identifier)) {
    console.log(`🔄 文件 ${identifier} 正在进行操作，跳过重复操作`);
    return
  }

  // 标记操作开始
  operationInProgress.value.add(identifier);

  try {
    console.log(`🚀 开始创建手动上传器恢复文件: ${identifier}`);
    console.log(`🚀 当前pausedByClose状态:`, localStorage.getItem('pausedByClose'));

    // 清理可能存在的旧实例
    const existingUploader = manualUploaders.value.get(identifier);
    if (existingUploader) {
      console.log(`🚀 清理旧的手动上传器实例: ${identifier}`);
      manualUploaders.value.delete(identifier);
    }

    // 额外清理：确保没有其他实例的状态干扰
    console.log(`准备为文件 ${identifier} 创建全新的手动上传器实例`);

    // 检查任务状态，确保只恢复暂停状态的任务
    const taskRecord = tableState.list.find(item => item.identifier === identifier);
    if (!taskRecord) {
      console.warn(`🔄 未找到文件 ${identifier} 对应的任务记录`);
      message.warning('未找到对应的任务记录');
      return;
    }

    console.log(`🔄 找到任务记录:`, {
      id: taskRecord.id,
      fileName: taskRecord.fileName,
      identifier: taskRecord.identifier,
      taskStatus: taskRecord.taskStatus
    });

    if (taskRecord.taskStatus === 1) {
      console.log(`🔄 任务 ${taskRecord.id} 已完成，无需恢复`);
      message.info('该任务已完成');
      return;
    }

    if (taskRecord.taskStatus === 3) {
      console.log(`🔄 任务 ${taskRecord.id} 已取消，无法恢复`);
      message.warning('该任务已取消，无法恢复');
      return;
    }

    if (taskRecord.taskStatus === 0) {
      console.log(`🔄 任务 ${taskRecord.id} 正在上传中，无需恢复`);
      message.info('该任务正在上传中');
      return;
    }

    if (taskRecord.taskStatus !== 2) {
      console.warn(`🔄 任务 ${taskRecord.id} 状态异常 (${taskRecord.taskStatus})，无法恢复`);
      message.warning('任务状态异常，无法恢复');
      return;
    }

    // 1. 从 IndexedDB 获取文件
    console.log(`🔄 尝试从 IndexedDB 获取文件: ${identifier}`);
    const fileData = await indexedDBUtil.getFile(identifier)
    if (!fileData) {
      console.error(`🔄 文件 ${identifier} 在本地存储中不存在，这可能是跨页面上传的问题`);
      message.error(`文件 ${identifier} 在本地存储中不存在，无法继续上传。请重新上传该文件。`)
      operationInProgress.value.delete(identifier)
      return
    }

    console.log(`🔄 成功从 IndexedDB 获取文件:`, {
      identifier: fileData.identifier,
      fileName: fileData.file?.name,
      fileSize: fileData.file?.size,
      totalChunks: fileData.totalChunks
    });

    // 2. 查询已上传的分片
    const response = await fileApi.getUploadedChunks({ identifier })
    if (!response.success) {
      message.error('查询上传进度失败')
      console.log(`查询进度失败，${identifier} 无法继续上传`);
      operationInProgress.value.delete(identifier)
      return
    }

    const uploadedChunks = response.data?.uploaded || []
    // 🔥 重要：验证和清理已上传分片数组
    const validUploadedChunks = [...new Set(uploadedChunks)].filter(chunk => chunk > 0 && chunk <= fileData.totalChunks);
    console.log(`文件 ${identifier} 已上传分片:`, uploadedChunks, `验证后的分片:`, validUploadedChunks)

    // 检查文件是否已经完全上传
    if (validUploadedChunks.length >= fileData.totalChunks) {
      console.log(`文件 ${identifier} 已经上传完成，无需继续上传 - 已上传分片: ${validUploadedChunks.length}/${fileData.totalChunks}`);
      
      // 从任务列表中获取任务ID，调用batchSuccess接口
      const taskRecord = tableState.list.find(item => item.identifier === identifier);
      if (taskRecord && taskRecord.id) {
        try {
          console.log(`文件已完成，调用batchSuccess接口，任务ID: ${taskRecord.id}`);
          await fileApi.batchSuccess([taskRecord.id]);
          console.log(`任务 ${taskRecord.id} 状态已设置为成功`);
          
          // 🔥 重要：文件上传完成后，立即标记为已完成状态，防止进度回退
          markFileAsCompleted(identifier);
          
          // 🔥 重要：立即刷新任务列表，确保UI状态更新
          await getTaskList();
          
          message.success('文件上传已完成');
          console.log(`文件已完成: ${identifier}`);
          
          // 确保进度显示为100%
          uploadStore.updateProgress(identifier, 1);
          console.log(`文件 ${identifier} 已完成，进度设置为100%`);
          
          // 延迟清理，确保UI能显示100%
          setTimeout(async () => {
            await cleanupCompletedFile(identifier);
          }, 1000);
        } catch (error) {
          console.error('设置任务成功状态失败:', error);
        }
      } else {
        // 确保进度显示为100%
        uploadStore.updateProgress(identifier, 1);
        console.log(`文件 ${identifier} 已完成，进度设置为100%`);
        
        message.success('文件上传已完成');
        console.log(`文件已完成: ${identifier}`);
        
        // 延迟清理，确保UI能显示100%
        setTimeout(async () => {
          await cleanupCompletedFile(identifier);
          await getTaskList();
        }, 1000);
      }
      
      return;
    }

    // 3. 创建全新的手动上传器实例，确保跨页面上传能正常工作
    console.log(`🔄 创建手动上传器实例，支持跨页面上传，大文件优化配置`);
    const manualUploader = new ManualUploader({
      chunkSize: 32 * 1024 * 1024, // 32MB分片，大文件优化
      maxConcurrentUploads: 6, // 6个并发，大文件优化
      onProgress: (progress) => {
        // 只有当新进度大于或等于当前进度时才更新，避免进度回退
        const currentProgress = uploadStore.state.progress[progress.identifier] || 0;
        const newProgress = progress.percentage / 100;

        if (newProgress >= currentProgress) {
          uploadStore.updateProgress(progress.identifier, newProgress);
          console.log(`🔄 跨页面手动上传器进度更新: ${progress.percentage.toFixed(2)}%, 当前进度: ${(currentProgress * 100).toFixed(2)}%`);
        } else {
          console.log(`🔄 跳过进度更新，新进度(${newProgress.toFixed(2)})小于当前进度(${currentProgress.toFixed(2)})`);
        }
      },
      onComplete: async (completedIdentifier) => {
        message.success('文件上传完成')
        console.log(`手动上传器完成: ${completedIdentifier}`);
        
        console.log(`🎯 手动上传器完成，开始处理完成逻辑: ${completedIdentifier}`);

        // 🔥 重要：立即标记为已完成状态，防止任何进度回退
        markFileAsCompleted(completedIdentifier);
        console.log(`✅ 文件已标记为完成状态: ${completedIdentifier}`);

        // 🔥 重要：立即锁定进度为100%
        uploadStore.updateProgress(completedIdentifier, 1);
        console.log(`✅ 进度已锁定为100%: ${completedIdentifier}`);

        // 清理手动上传器实例
        manualUploaders.value.delete(completedIdentifier)

        // 通知原生上传组件，这个文件的进度控制已结束
        emitter.emit('mark.progress.source', { identifier: completedIdentifier, isManual: false });

        try {
          // 🔥 重要：延迟刷新任务列表，避免立即刷新导致进度回退
          setTimeout(async () => {
            try {
              console.log(`🔄 延迟刷新任务列表: ${completedIdentifier}`);
              await getTaskList();
              console.log(`✅ 任务列表已刷新: ${completedIdentifier}`);
            } catch (error) {
              console.error(`刷新任务列表失败: ${completedIdentifier}`, error);
            }
          }, 1500); // 延迟1.5秒刷新任务列表

          // 延迟清理，确保UI能显示100%足够长时间
          setTimeout(async () => {
            try {
              // 使用统一清理函数
              await cleanupCompletedFile(completedIdentifier);
              console.log(`✅ 已清理文件 ${completedIdentifier} 的所有数据`);

              // 清除浏览器关闭标记（如果存在）
              const pausedByClose = localStorage.getItem('pausedByClose');
              if (pausedByClose === 'true') {
                localStorage.removeItem('pausedByClose');
                console.log('✅ 已清除浏览器关闭标记');
              }
            } catch (error) {
              console.error(`❌ 清理文件 ${completedIdentifier} 相关数据时出错:`, error);
            }
          }, 1500); // 减半到1.5秒，确保用户能看到100%状态
        } catch (error) {
          console.error(`❌ 处理完成逻辑失败: ${completedIdentifier}`, error);
        }
      },
      onError: (error, errorIdentifier) => {
        console.error('上传错误:', error)
        console.log(`手动上传器错误: ${errorIdentifier}`);

        // 检查是否是暂停导致的错误
        if (error.message === '上传已暂停') {
          message.info('文件上传已暂停')
          console.log(`文件 ${errorIdentifier} 上传已暂停，保留手动上传器实例以便后续恢复`)
          // 暂停时不删除手动上传器实例，保留以便后续恢复
        } else {
          // 只有在真正的错误时才删除手动上传器实例
          manualUploaders.value.delete(errorIdentifier)
          message.error(`文件上传失败: ${error.message}`)
          // 通知原生上传组件，这个文件的进度控制已结束
          emitter.emit('mark.progress.source', { identifier: errorIdentifier, isManual: false });
        }
      }
    })

    // 存储手动上传器实例
    manualUploaders.value.set(identifier, manualUploader)
    
    // 确保清除该文件的暂停状态 - 在开始上传前多次确认
    manualUploader.resetAllStates(); // 完全重置所有状态
    manualUploader.resumeUpload(identifier);
    manualUploader.clearAllPausedStates(); // 清理所有可能的暂停状态
    manualUploader.forceResetUploadState(identifier); // 强制重置状态
    
    // 再次确认没有暂停状态
    console.log(`文件 ${identifier} 暂停状态检查:`, manualUploader.isPaused(identifier));

    // 4. 开始上传
    console.log(`开始恢复上传: ${fileData.file.name}，已上传分片: ${validUploadedChunks.length}/${fileData.totalChunks}`);
    message.info(`开始恢复上传: ${fileData.file.name}`);
    
    // 获取任务信息
    const filePath = taskRecord?.filePath || '';
    // 🔥 重要：优先使用原始文件名，避免数据库中被截断的文件名
    const fileName = fileData.file.name || taskRecord?.fileName;
    const taskId = taskRecord?.id;

    console.log(`🔄 准备上传参数:`, {
      identifier: identifier,
      fileName: fileName,
      filePath: filePath,
      taskId: taskId,
      originalFileName: fileData.file.name,
      uploadedChunks: validUploadedChunks.length,
      totalChunks: fileData.totalChunks
    });

    // 确保使用现有的任务ID，不创建新任务
    if (!taskId) {
      console.error(`🔄 任务ID为空，无法继续上传`);
      message.error('任务ID为空，无法继续上传');
      return;
    }

    // 在开始上传前，再次确保没有暂停状态，并添加额外的保护
    console.log(`最终暂停状态检查 - 文件: ${identifier}, 暂停状态: ${manualUploader.isPaused(identifier)}`);
    if (manualUploader.isPaused(identifier)) {
      console.warn(`检测到意外的暂停状态，强制清除`);
      manualUploader.resumeUpload(identifier);
      manualUploader.forceResetUploadState(identifier);
    }

    console.log(`🚀 开始调用 manualUploader.uploadFile，使用现有任务ID: ${taskId}`);
    await manualUploader.uploadFile(fileData.file, identifier, validUploadedChunks, filePath, fileName, taskId)
    console.log(`✅ manualUploader.uploadFile 调用完成`);

  } catch (error) {
    console.error('恢复上传失败:', error)
    // message.error('恢复上传失败')
    console.log(`恢复上传失败: ${identifier}`);
    // 清理手动上传器实例
    manualUploaders.value.delete(identifier)
  } finally {
    // 清理操作标记
    operationInProgress.value.delete(identifier);
  }
}

async function onChangeTable({ current, pageSize }) {
  tableState.pagination.current = current
  tableState.pagination.pageSize = pageSize

  await getTaskList()
}

/**
 * 防抖刷新任务列表，避免频繁调用接口
 */
function debounceRefreshTaskList() {
  // 清除之前的定时器
  if (refreshTimer.value) {
    clearTimeout(refreshTimer.value)
  }

  // 设置新的定时器，1.5秒后执行
  refreshTimer.value = setTimeout(async () => {
    try {
      await getTaskList()
      console.log('防抖刷新任务列表完成')
    } catch (error) {
      console.error('防抖刷新任务列表失败:', error)
    }
  }, 1500)

  console.log('已设置防抖刷新任务列表，1.5秒后执行')
}

watch(() => uploadStore.state.progress, (val) => {
  for (const key in val) {
    if (Object.prototype.hasOwnProperty.call(val, key)) {
      const progress = val[key];
      if (progress === 1) {
        // 文件上传完成后，标记该文件为已完成，防止后续进度恢复时被重置
        markFileAsCompleted(key);
        // 使用防抖函数，避免频繁调用接口
        debounceRefreshTaskList()
      }
    }
  }
}, { deep: true })

/**
 * 标记文件为已完成状态，防止进度被重置
 */
function markFileAsCompleted(identifier) {
  try {
    const completedFiles = getCompletedFiles();
    if (!completedFiles.includes(identifier)) {
      completedFiles.push(identifier);
      localStorage.setItem('completedFileIdentifiers', JSON.stringify(completedFiles));
      console.log(`文件 ${identifier} 已标记为完成状态，防止进度重置`);
    }
  } catch (error) {
    console.error('标记文件完成状态失败:', error);
  }
}

/**
 * 获取已完成的文件标识符列表
 */
function getCompletedFiles() {
  try {
    const completedFiles = localStorage.getItem('completedFileIdentifiers');
    return completedFiles ? JSON.parse(completedFiles) : [];
  } catch (error) {
    console.error('获取已完成文件列表失败:', error);
    return [];
  }
}

/**
 * 检查文件是否已完成
 */
function isFileCompleted(identifier) {
  const completedFiles = getCompletedFiles();
  return completedFiles.includes(identifier);
}

/**
 * 获取文件进度，确保已完成文件显示100%
 */
function getFileProgress(record) {
  if (!record.identifier) {
    return 0;
  }

  // 🔥 重要：如果文件已标记为完成，强制显示100%
  if (isFileCompleted(record.identifier)) {
    console.log(`🛡️ 文件 ${record.identifier} 已完成，强制显示100%进度`);
    return 100;
  }

  // 获取存储的进度
  const progress = uploadStore.state.progress[record.identifier] || 0;
  const percent = Math.floor(progress * 100);

  // 🔥 重要：如果进度已经是100%，立即标记为已完成
  if (percent >= 100) {
    markFileAsCompleted(record.identifier);
    return 100;
  }

  return percent;
}


watch(() => props.visible, async (val) => {
  if (val) {
    console.log("🚀 ~ watch ~ uploadStore.state1:", uploadStore.state)
    activeTabKey.value = 0
    
    // 检查是否是浏览器关闭后重新打开
    const pausedByClose = localStorage.getItem('pausedByClose');
    
    // 获取任务列表
    await getTaskList()
    
    // 优先恢复上传进度，确保进度在任务列表显示前就已经恢复
    if (activeTabKey.value === 0) {
      await restoreUploadProgress()
    }
    
    // 如果是浏览器关闭后重新打开，并且在"进行中"标签
    if (pausedByClose === 'true' && activeTabKey.value === 0) {
      console.log('🔄 检测到浏览器关闭后重新打开，任务应该已被暂停，按钮状态应为"继续"');

      // 立即检查当前任务状态
      const currentInProgressTasks = tableState.list.filter(task => task.taskStatus === 0);
      if (currentInProgressTasks.length > 0) {
        console.log('🔄 发现仍有进行中的任务，可能是浏览器关闭时暂停接口未成功调用:', currentInProgressTasks);

        // 立即调用暂停接口，确保这些任务被正确暂停
        const taskIds = currentInProgressTasks.map(task => task.id);
        console.log('🔄 立即调用暂停接口，任务IDs:', taskIds);

        fileApi.batchPause(taskIds).then(async res => {
          console.log('🔄 补充暂停接口调用成功');
          // 刷新任务列表
          await getTaskList();
          console.log('🔄 任务列表已刷新，按钮状态应显示为"继续"');
        }).catch(error => {
          console.error('🔄 补充暂停接口调用失败:', error);
          // 即使接口失败，也强制设置本地状态
          tableState.list.forEach(task => {
            if (task.taskStatus === 0) {
              task.taskStatus = 2; // 强制设置为暂停状态
            }
          });
          console.log('🔄 已强制设置本地状态为暂停');
        });
      } else {
        console.log('🔄 所有任务状态正确，无需额外处理');
      }

      // 注意：不要在这里清除 pausedByClose 标记，
      // 只有在手动上传器完成上传后才清除，以区分两种上传模式
      console.log('保留 pausedByClose 标记，用于区分上传模式');
    }
  }
})

/**
 * 恢复上传进度
 */
async function restoreUploadProgress() {
  try {
    console.log('开始恢复上传进度...')
    
    // 1. 首先检查store中是否已有进度数据
    const currentProgress = uploadStore.state.progress
    console.log('当前store中的进度:', currentProgress)
    
    // 2. 从 IndexedDB 获取所有文件
    const allFiles = await indexedDBUtil.getAllFiles()
    
    if (allFiles.length === 0) {
      console.log('IndexedDB中没有文件，跳过进度恢复')
      return
    }
    
    console.log('从 IndexedDB 获取到的文件:', allFiles)
    
    // 3. 遍历每个文件，查询后端已上传的分片并同步进度
    for (const fileData of allFiles) {
      try {
        // 🔥 重要：检查文件是否已完成，如果已完成则跳过进度恢复，防止进度回退
        if (isFileCompleted(fileData.identifier)) {
          console.log(`文件 ${fileData.identifier} 已标记为完成，跳过进度恢复，保持100%进度`);
          // 确保进度保持在100%
          uploadStore.updateProgress(fileData.identifier, 1);
          continue;
        }
        
        const response = await fileApi.getUploadedChunks({
          identifier: fileData.identifier
        })
        
        if (response.success && response.data) {
          const uploadedChunks = response.data.uploaded || []
          const maxUploadedChunk = uploadedChunks.length > 0 ? Math.max(...uploadedChunks) : 0
          
          // 4. 计算已上传进度 - 使用已上传分片数量而不是最大分片号
          const serverProgress = fileData.totalChunks > 0 ? uploadedChunks.length / fileData.totalChunks : 0
          const currentFileProgress = currentProgress[fileData.identifier] || 0
          
          // 5. 确保进度不会倒退 - 更严格的进度保护
          let finalProgress;
          
          // 记录进度来源，用于调试
          let progressSource = '';
          
          // 🔥 重要：如果当前进度已经是100%，不允许任何回退
          if (currentFileProgress >= 1) {
            finalProgress = 1;
            progressSource = '本地(已完成)';
            // 同时标记为已完成
            markFileAsCompleted(fileData.identifier);
          } else if (currentFileProgress >= serverProgress) {
            // 如果本地进度更高，保持本地进度
            finalProgress = currentFileProgress;
            progressSource = '本地';
          } else {
            // 如果服务器进度更高，使用服务器进度
            finalProgress = serverProgress;
            progressSource = '服务器';
          }
          
          console.log(`文件 ${fileData.identifier} 进度比较 - 本地: ${(currentFileProgress * 100).toFixed(2)}%, 服务器: ${(serverProgress * 100).toFixed(2)}%, 最终使用: ${progressSource} ${(finalProgress * 100).toFixed(2)}%`);
          
          // 6. 更新进度到 store（只有当进度发生变化时才更新）
          if (Math.abs(finalProgress - currentFileProgress) > 0.001) {
            uploadStore.updateProgress(fileData.identifier, finalProgress)
            console.log(`文件 ${fileData.identifier} 进度已更新: ${(finalProgress * 100).toFixed(2)}% (服务器: ${(serverProgress * 100).toFixed(2)}%, 本地: ${(currentFileProgress * 100).toFixed(2)}%)`)
          } else {
            console.log(`文件 ${fileData.identifier} 进度无需更新: ${(finalProgress * 100).toFixed(2)}%`)
          }
        }
      } catch (error) {
        console.error(`查询文件 ${fileData.identifier} 上传进度失败:`, error)
      }
    }
    
    console.log('上传进度恢复完成')
  } catch (error) {
    console.error('恢复上传进度失败:', error)
  }
}

/**
 * 获取已完成的任务ID列表
 */
function getCompletedTasks() {
  try {
    const completedTasks = localStorage.getItem('completedTaskIds');
    return completedTasks ? JSON.parse(completedTasks) : [];
  } catch (error) {
    console.error('获取已完成任务列表失败:', error);
    return [];
  }
}

/**
 * 清理已完成的任务列表（可选，用于定期清理localStorage）
 */
function clearCompletedTasks() {
  try {
    localStorage.removeItem('completedTaskIds');
    console.log('已完成任务列表已清理');
  } catch (error) {
    console.error('清理已完成任务列表失败:', error);
  }
}

/**
 * 监听原生上传器的文件状态变化（仅用于日志记录）
 */
function syncNativeUploaderState() {
  // 监听来自原生上传器的文件状态变化
  emitter.on('file.upload.started', ({ identifier }) => {
    if (identifier) {
      console.log(`原生上传器开始上传文件: ${identifier}`);
    }
  });

  emitter.on('file.upload.paused', ({ identifier }) => {
    if (identifier) {
      console.log(`原生上传器暂停文件: ${identifier}`);
    }
  });

  emitter.on('file.upload.completed', async ({ identifier }) => {
    if (identifier) {
      console.log(`文件上传完成: ${identifier}`);

      // 🔥 重要：确保文件上传完成后清除所有相关数据（双重保险）
      try {
        await cleanupCompletedFile(identifier);
        console.log(`✅ 事件监听器：已清理完成文件的所有数据: ${identifier}`);
      } catch (error) {
        console.error(`❌ 事件监听器：清理完成文件数据失败: ${identifier}`, error);
      }
    }
  });
}

onMounted(() => {
  syncNativeUploaderState();
})

onUnmounted(() => {
  // 清理事件监听器
  emitter.off('file.upload.started');
  emitter.off('file.upload.paused');
  emitter.off('file.upload.completed');

  // 清理防抖定时器
  if (refreshTimer.value) {
    clearTimeout(refreshTimer.value);
    refreshTimer.value = null;
    console.log('已清理防抖定时器');
  }
})

</script>
<template>
  <a-modal :open="props.visible" wrapClassName="middle-modal" width="950px" title="任务执行列表" :footer="null"
    @cancel="cancel">
    <div class="task-container">
      <!-- <div class="task-type">
        <span v-for="type in typeList" :class="curTypeKey === type.key ? 'actived' : ''" :key="type.key"
          @click="onChangeType(type)">
          {{ type.label }}
        </span>
      </div> -->

      <div class="task-table">
        <div class="task-tabs">
          <div v-for="tab in statusList" :key="tab.key" class="task-tab"
            :class="activeTabKey === tab.key ? 'actived' : ''" @click="onChangeTab(tab)">
            {{ tab.label }} ({{ tab.num }})
          </div>
        </div>
        <div class="table-warpper">
          <a-button v-if="activeTabKey == 0" type="primary" style="margin-bottom: 8px;"
            @click="onCancelTask()">批量取消</a-button>
          <a-button v-else type="primary" style="margin-bottom: 8px;" @click="onDeleteTask()">批量删除</a-button>
          <a-table class="normal-table-component" row-key="id" :columns="tableState.columns" :scroll="{ y: 210 }"
            :row-selection="{ selectedRowKeys: tableState.selectedRowKeys, onChange: onSelectChange }"
            :data-source="tableState.list" :pagination="tableState.pagination" :loading="tableState.loading"
            @change="onChangeTable">
            <template #bodyCell="{ column, record, text, index }">
              <template v-if="column.dataIndex === 'progress'">
                <!-- 如果任务状态是已完成(1)，则直接显示"已完成"，不显示进度条 -->
                <span v-if="record.taskStatus === 1">已完成</span>
                <!-- 如果任务状态是已取消(3)，则显示"已取消" -->
                <span v-else-if="record.taskStatus === 3">已取消</span>
                <!-- 其他情况（上传中或暂停）显示进度条 -->
                <a-progress v-else
                  :percent="getFileProgress(record)" trailColor="#fff" />
              </template>
              <template v-if="column.dataIndex === 'operate'">
                <a-flex :gap="8" justify="center" align="center">
                  <template v-if="record.taskStatus == 0">
                    <!-- taskStatus = 0: 上传中，显示暂停按钮 -->
                    <a @click="onPauselTask(record)" style="cursor: pointer;">暂停</a>
                    <a class="red-7" @click="onCancelTask(record)">取消</a>
                  </template>
                  <template v-else-if="record.taskStatus == 2">
                    <!-- taskStatus = 2: 暂停，显示继续按钮 -->
                    <a @click="onResumelTask(record)">继续</a>
                    <a class="red-7" @click="onCancelTask(record)">取消</a>
                  </template>
                  <a v-if="record.taskStatus != 0 && record.taskStatus != 2" class="red-7" @click="onDeleteTask(record)">删除</a>
                </a-flex>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.task-container {
  height: 400px;
  overflow: hidden;
  display: flex;
  margin: -16px;

  .task-type {
    width: 64px;
    height: 100%;
    border-right: 1px solid @gray-9;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;

    span {
      text-align: center;
      width: 100%;
      line-height: 24px;
      display: inline-block;
      color: #fff;
      cursor: pointer;

      &:hover,
      &.actived {
        background-color: #1F242F;
      }
    }
  }

  .task-table {
    // width: calc(100% - 64px);
    width: 100%;
    padding: 0;

    .task-tabs {
      display: flex;
      align-items: center;
      gap: 16px;
      color: #fff;
      line-height: 32px;
      padding: 4px 12px 0;
      border-bottom: 1px solid @gray-9;

      .task-tab {
        cursor: pointer;
        padding-bottom: 4px;

        &.actived,
        &:hover {
          color: #2970ff;
          border-bottom: 2px solid #2970ff;
        }
      }
    }

    .table-warpper {
      padding: 8px 12px;
      height: 340px;
    }
  }
}

.red-7 {
  color: @red-7;
}

.uploading {
  color: #1890ff;
  cursor: not-allowed;
  opacity: 0.6;
}

// 任务操作按钮样式
.task-table {
  a {
    color: #1890ff;
    text-decoration: none;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
    transition: all 0.2s;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
    }

    &.red-7 {
      color: @red-7;

      &:hover {
        background-color: rgba(245, 34, 45, 0.1);
      }
    }
  }
}

::v-deep .ant-progress-text {
  color: #fff;
}
</style>