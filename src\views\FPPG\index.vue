<script setup>
import { ref, reactive, defineProps, onMounted, watch, nextTick } from 'vue';
import DataContent from '@/components/dataContent/index.vue';
import folderTree from '@/components/tree/folder.vue';

const folderData = ref(null)

function onChange(data) {
    folderData.value = data
}
</script>

<template>
    <div class="base-layout-lr">
        <div class="left-content">
            <folder-tree @change="onChange"></folder-tree>
        </div>
        <div class="right-content">
            <DataContent :step="'FPPG'" :folder="folderData"> </DataContent>
        </div>
    </div>
</template>

<style scoped lang="less">
.base-layout-lr {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .left-content {
        width: 280px;
        flex-shrink: 0;
        height: 100%;
        background-color: rgba(12, 17, 29, 0.7);
        padding: 12px 0;
    }

    .right-content {
        width: calc(100% - 280px);
        height: 100%;
    }
}
</style>