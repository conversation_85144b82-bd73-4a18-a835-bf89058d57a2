  @base-color: #06122A;

  :deep(.ant-form-item-required) {
    color: #9BB4BD !important;
    height: 38px !important;
    padding: 8px 0;
  }



  ::v-deep {

    .custom-tabs>.ant-tabs-nav,
    .custom-tabs>div>.ant-tabs-nav {
      margin: 0 12px;
    }

    .custom-tabs .ant-tabs-tab {
      padding-top: 8px !important;
    }
  }



  .ant-tabs {
    color: @gray-5;
  }

  :deep(.ant-tabs .ant-tabs-tab) {
    padding-top: 0;
  }

  .main {
    width: 100%;
    height: 100%;
    // background-color: @base-color;
    display: flex;
  }

  .icon-cursor {
    cursor: pointer;
  }

  .left {
    width: 280px;
    height: 100%;
    border-right: 1px solid @gray-9;
    background: rgba(12, 17, 29, 0.9);
    display: flex;
    flex-direction: column;

    .left-head {
      padding: 16px;
      padding-top: 0;

      .left-head-title {
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        color: #F5F5F6;
      }
    }

    :deep(.ant-input-affix-wrapper) {
      border-radius: 2px;
      padding: 3px 8px;
    }

    :deep(.ant-input) {
      background-color: transparent;
    }

    .left-tree {
      flex: 1;
      padding: 0;
      overflow-y: auto;

      .tree-container {
        padding: 8px 0px;
      }

      :deep(.ant-tree) {
        background: transparent;

        .ant-tree-treenode {
          align-items: center;
          padding: 0;

          .model-tree-action-dropdown {
            visibility: hidden;
          }

          &.ant-tree-treenode-selected,
          &:hover {
            background: #1F242F;

            .model-tree-action-dropdown {
              visibility: visible;
            }

          }
        }
      }
    }
  }

  .container {
    padding: 16px;
    display: flex;
    flex-direction: column;
    width: calc(100% - 256px);
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    flex: 1;
  }

  .radio {
    height: 34px;
  }

  .content {
    flex: 1;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;
    overflow-y: auto;
    background-color: rgba(12, 17, 29, 0.7);
    border: 1px solid @gray-9;
    border-radius: 2px;
  }

  :deep(.ant-radio-group) {
    border-radius: 4px;
    height: 34px;
  }

  :deep(.ant-radio-button-wrapper) {
    background-color: rgba(#458EFF, 0.1);
    border-color: #458EFF;
    height: 34px;
    line-height: 34px;
  }

  :deep(.ant-radio-button-checked) {
    background-color: #458EFF;
  }

  :deep(.ant-form-item):first-child {
    height: 72px;
    font-size: 14px;
    margin: 8px 0 0 0;
  }

  :deep(.ant-form-item) {
    height: 72px;
    font-size: 14px;
    margin: 16px 0;
  }

  :deep(.ant-col) {
    height: 38px;
    line-height: 22px;
    font-size: 14px;
    padding: 0;
  }

  // :deep(.ant-input) {
  //   background-color: rgba(#458EFF, 0.1);
  //   border-color: #458EFF;
  //   color: white;
  // }

  // :deep(.ant-input)::placeholder {
  //   color: #9BB4BD;
  // }

  .button {
    margin-bottom: 18px;
    border-radius: 4px;
    height: 34px;
    width: 62px;
  }

  .title {
    margin-top: 18px;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 16px;
  }

  .table {
    flex: 1;
  }

  :deep(.ant-badge-status-text) {
    color: #ffffff !important;
    font-size: 12px !important;
  }

  .operate-button {
    color: #2970FF;
    width: 32px;
  }

  // td {
  //   background-color: rgba(#458EFF, 0.15);
  // }

  // :deep(.ant-table-thead) {
  //   line-height: 0;

  //   tr th {
  //     background-color: #458EFF;
  //     height: 38px;
  //     color: white;
  //     font-size: 14px;
  //     font-weight: 400;

  //     &::before {
  //       width: 0 !important;
  //     }
  //   }
  // }

  // :deep(.ant-table-header) {
  //   height: 38px;
  // }

  // :deep(.table-striped) td {
  //   background-color: rgba(#458EFF, 0.1);
  // }

  // .normal-table-component {
  //   background-color: @base-color;
  // }

  :deep(.ant-spin-nested-loading) {
    height: 100%;
  }

  :deep(.ant-spin-container),
  :deep(.ant-table-container) {
    height: 100%;
  }

  :deep(.ant-table-wrapper) {
    height: calc(100% - 56px);
  }

  :deep(.ant-table) {
    height: calc(100% - 56px) !important;
  }

  // .content {
  //   gap: 24px;
  // }


  // :deep(.ant-spin-nested-loading) {}

  // :deep(.ant-pagination) {
  //   margin: 24px 0 0 0 !important;

  //   .ant-pagination-item {
  //     height: 28px;
  //     line-height: 28px;
  //     width: 28px;
  //     min-width: 28px;
  //     border: 1px solid #458EFF;
  //     background-color: @base-color;

  //     a {
  //       color: #9BB4BD;
  //     }
  //   }
  // }

  // :deep(.ant-table-cell) {
  //   padding: 13px 16px !important;
  // }

  // .ant-table-empty .ant-table-body {
  //   height: 100%;
  // }

  // .ant-table-empty .ant-table-body table {
  //   height: 100%;
  // }

  // :deep(.ant-table-body) {
  //   overflow-y: auto !important;
  // }

  // :deep(.ant-pagination-item-active) {
  //   background-color: rgba(#458EFF, 0.1) !important;

  //   a {
  //     color: #9BB4BD !important;
  //   }
  // }

  // .ant-select-selection-item {
  //   color: #9BB4BD;
  // }

  // :deep(.ant-select-selector) {
  //   background-color: rgba(#458EFF, 0.1) !important;
  //   border-color: #458EFF !important;
  //   border-radius: 4px;
  // }

  // .ant-pagination-item-ellipsis {
  //   color: #9BB4BD !important;
  // }

  // .ant-pagination-options-quick-jumper {
  //   color: #9BB4BD !important;

  //   input {
  //     border-color: #458EFF !important;
  //     background-color: rgba(#458EFF, 0.1) !important;
  //     color: white !important;
  //     border-radius: 4px !important;
  //   }
  // }

  .chart-warp {
    display: flex;
    width: 100%;
    gap: 22px;

    .chart-item {
      width: calc(50% - 14px);
      border-radius: 4px;
      border: 1px solid @gray-9;
      background: @gray-12;
      // box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      height: 400px;
      flex-shrink: 0;
      position: relative;
      display: flex;
      align-items: center;

      .chart-container {
        width: 55%;
        height: 100%;
        flex-shrink: 0;
      }

      .chart-title {
        position: absolute;
        color: #FFF;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 171.429% */
        top: 0;
        left: 0;
        padding-left: 16px;
        padding-top: 8px;
        padding-bottom: 8px;
        width: 100%;
        border-bottom: 1px solid @gray-9;
      }

      .chart-legend {
        min-width: 252px;
        flex-shrink: 0;

        >span {
          display: flex;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          color: #FFF;
          align-items: center;
          gap: 22px;

          .chart-legend-value {
            width: 44px;
          }

          .chart-legend-label {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 90px;

            &::before {
              content: '';
              display: block;
              width: 10px;
              height: 10px;
              flex-shrink: 0;
              background: #2970FF;
            }

            &.green::before {
              content: '';
              display: block;
              width: 10px;
              height: 10px;
              flex-shrink: 0;
              background: #17B26A;
            }
          }
        }
      }
    }
  }

  // .left-tree, .content {
  //   &::-webkit-scrollbar {
  //     width: 6px;
  //   }

  //   &::-webkit-scrollbar-thumb {
  //     background-color: rgba(0, 0, 0, 0.2);
  //     border-radius: 3px;
  //   }

  //   &::-webkit-scrollbar-track {
  //     background-color: rgba(0, 0, 0, 0.05);
  //   }

  //   scrollbar-width: thin;
  // }