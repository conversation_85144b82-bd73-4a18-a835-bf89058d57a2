<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, nextTick } from 'vue';
  import { MoreOutlined } from '@ant-design/icons-vue';
  import {message} from 'ant-design-vue';
  import config from './components/config.vue';
  import monitor from './components/monitor.vue';
  import viewData from './components/view.vue';
  import abTree from '@/components/tree/index.vue';
  import bussinessApi from '@/api/bussiness/bussiness.api';
  import ProtocolApi from '@/api/protocol/protocol.api';
  import breadcrumb from '../home/<USER>/breadcrumb.vue';
  enum contentEnum {
    view = 'VIEW', // 概览
    list = 'LIST', // 主题列表
    detail = 'DETAIL', // 主题对应数据
  }

  // 基本属性
  const state: any = reactive({
    keywords: undefined, // 检索关键字
    loading: false,
    content: contentEnum.view,
    id: null,
    tableName: undefined,
    type: 1 // 1：域 2: 系统
  })

  const treeRef = ref(null);
  const treeState: any = reactive({
    expandedKeys: [],
    selectedKeys: ['view'],

    list: [
      { name: '概览', type: contentEnum.view, key: 'view', isLeaf: true }
    ]
  })

  const treeFieldNames = {
    children: 'systems',
    title: 'name',
    key: 'key'
  }

  // 统一操作方法
  function handle(type: any, record?: any) {
    console.log(type, record)
    switch (type) {
      case 'openFolder':
        // 展开所有文件夹
        treeState.expandedKeys = getTreeNodeKey(treeState.list)
        break;
      case 'closeFolder':
        // 关闭所有文件夹
        treeState.expandedKeys = []
        break;
      case 'refresh':
        refreshTree()
        break;
      case 'search':
        // 检索
        // getTreeData()
        break;
      default:
        break;
    }
  };

  async function refreshTree() {
    // state.keywords = undefined
    // state.loading = true
    await getTreeData()
  }

  /**
   * 获取嵌套数组的key
   * @param arr 
   */
  function getTreeNodeKey(arr: any[]) {
    const result = []
    for (const item of arr) {
      if (item.children && Array.isArray(item.children)) {
        const arr: any = getTreeNodeKey(item.children)
        result.push(...arr)
      }
      result.push(item.id);
    }
    return result
  }

  // 监听左侧数据树选择变化
  function selectTreeNode(selectedKeys: any, e: any) {
    // console.log("🚀 , e:", e.node.id)
    const { id, type, business, name } = e.node
    console.log(id, type, business, name)
    state.content = type
    state.id = id
    if (business) {
      state.type = 2
    } else {
      state.type = 1
    }
    if (type === contentEnum.detail) {
      state.tableName = name
    }
  }


  /**
   * 加载树目录数据
   * @param treeNode 
   */
  const onLoadTreeData = (treeNode: any) => {
    return new Promise < void> (async resolve => {
      if (treeNode.dataRef.systems || treeNode.dataRef.isLeaf) {
        resolve();
        return;
      }
      if (!treeNode.dataRef.isLeaf) {
        const data: any = await ProtocolApi.getProtocolListBySystem(treeNode.dataRef.id)
        treeNode.dataRef.systems = []
        for (const item of data?.data) {
          treeNode.dataRef.systems.push({
            ...item,
            type: contentEnum.detail,
            isLeaf: true
          })
        }
        resolve();
      }
    })
  }

  async function getTreeData() {
    treeState.list = [{ name: '概览', type: contentEnum.view, key: 'view', isLeaf: true }]
    const res = await bussinessApi.getBussinessSystems()
    if (res?.data && Array.isArray(res.data)) {
      for (const area of res.data) {
        let areaObj = {
          ...area,
          type: contentEnum.list,
          key: area.id + area.name,
          region: area.id
        }
        areaObj.systems = area.systems.map((item: any) => {
          return { ...item, type: contentEnum.list, key: item.id + item.name, isLeaf: false, region: area.id, business: item.id }
        })
        treeState.list.push(areaObj)
      }
    }
  }

  async function getSystemProtocols(id: any) {
    const res = await bussinessApi.getBussinessProtocols(id, 2)
    return res
  }

  function onClickSystem(record: any) {
    state.content = contentEnum.list
    state.id = record.systemId
    state.type = 2
  }

  function onSubcribe(record: any, status: number) {
    console.log('record',record);
    
    const params = {
      domainId: !record.business?record.id:'',
      systemId: record.business?record.id:'',
      protocolId:  record.type == 'DETAIL' ? record.id : '',
      status: status
    }

     ProtocolApi.subscribeProtocol(params).then(res => {
      console.log('订阅参数：', params)

      if(res.data){
        message.success('操作成功！')
      }
      state.id = undefined
      nextTick(() => {
        state.content = contentEnum.list
        state.id = record.id
      })
    })
  }

  watch(() => state.content, (val => {
    sessionStorage.setItem('state.content', val)
  }))
  watch(() =>state.type, (val => {
    sessionStorage.setItem('state.type', val)
  }))
  watch(() =>state.id, (val => {
    sessionStorage.setItem('state.id', val)
  }))

  watch(() => treeState.expandedKeys, (val => {
    sessionStorage.setItem('treeState.expandedKeys', JSON.stringify(val))
  }), { deep: true })
  watch(() => treeState.selectedKeys, (val => {
    sessionStorage.setItem('treeState.selectedKeys', JSON.stringify(val))
  }), { deep: true })
  watch(() => state.tableName, (val => {
    sessionStorage.setItem('state.tableName', val)
  }), { deep: true })
  onMounted(() => {
    getTreeData()
    const cache = {
      type: sessionStorage.getItem('state.type'),
      id: sessionStorage.getItem('state.id'),
      content: sessionStorage.getItem('state.content'),
      expandedKeys: sessionStorage.getItem('treeState.expandedKeys'),
      selectedKeys: sessionStorage.getItem('treeState.selectedKeys'),
      tableName: sessionStorage.getItem('state.tableName'),
    }
    // 初始选择预览
    if (cache.expandedKeys) {
      treeState.expandedKeys = JSON.parse(cache.expandedKeys)
    }
    if (cache.selectedKeys) {
      treeState.selectedKeys = JSON.parse(cache.selectedKeys)
    }
    if (cache.content) {
      state.content = cache.content
    }
    if (cache.type) {
      state.type = cache.type
    }
    if (cache.id) {
      state.id = cache.id
    }
    if (cache.tableName) {
      state.tableName = cache.tableName
    }
  })
</script>

<template>
  <a-config-provider>
    <div class="main">
      <div class="left">
        <div class="left-head">
          <a-flex justify="space-between" align="center">
            <span class="left-head-title">配置</span>
            <a-flex :gap="8">
              <SvgIcon name="folderOpen" className="icon-cursor" @click="handle('openFolder')" />
              <SvgIcon name="folderClose" className="icon-cursor" @click="handle('closeFolder')" />
              <SvgIcon name="refresh" className="icon-cursor" @click="handle('refresh', true)" />
            </a-flex>
          </a-flex>
          <a-input placeholder="请输入名称进行检索" v-model:value="state.keywords" @pressEnter="handle('search')"
            @change="handle('search')" allow-clear style="margin-top: 8px;">
            <template #prefix>
              <SvgIcon name="search" />
            </template>
          </a-input>
        </div>
        <div class="left-tree">
          <ab-tree ref="treeRef" :fieldNames="treeFieldNames" v-model:expandedKeys="treeState.expandedKeys"
            v-model:selectedKeys="treeState.selectedKeys" :treeData="treeState.list" showIcon blockNode
            :load-data="onLoadTreeData" @select="selectTreeNode">

            <template #title="{ dataRef }">

              <span class="tree-node-title" :title="dataRef.name">{{ dataRef.name }}</span>

            </template>
            <template #icon="{ dataRef }">
              <SvgIcon v-if="dataRef.type === contentEnum.detail" name="modelIcon" className="tree-node-icon" />
              <SvgIcon v-else className="tree-node-icon" name="folder" />
            </template>
            <template #nodeAction="{ record }">
              <a-dropdown v-if="record.key !== 'view'" class="model-tree-action-dropdown">
                <span class="ant-dropdown-link">
                  <MoreOutlined />
                </span>
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <a @click.stop="onSubcribe(record, 1)">订阅</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a @click.stop="onSubcribe(record, 0)">断开</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </ab-tree>
        </div>
      </div>
      <div class="container">
        <breadcrumb></breadcrumb>
        <div class="content">
          <config v-if="state.content === contentEnum.list" :id="state.id" :type="state.type"></config>
          <monitor v-else-if="state.content === contentEnum.detail" :tableName="state.tableName"></monitor>
          <view-data v-else @click="onClickSystem" />
        </div>
      </div>
    </div>
  </a-config-provider>
</template>

<style scoped lang="less">
  @import './sytle.less';

  /* 添加树节点标题样式 */
  :deep(.ant-tree-node-content-wrapper) {
    overflow: hidden;
  }

  :deep(.base-tree-content) {
    padding-right: 0;
  }

  :deep(.base-tree-node-title) {
    display: inline-block;
    max-width: calc(100% - 28px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }
</style>