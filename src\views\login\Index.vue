<template>
    <div class="login-container">
        <div class="login-left">
            <img :src="myImg">
        </div>
        <div class="login-right">

            <div class="login-form" @keyup.enter="submitLogin">
                <div class="system-name">
                    <img :src="logoName">
                    <span>数据管理系统</span>
                </div>
                <div class="title">
                    <span>登录</span>
                </div>
                <div class="desc">
                    <span>欢迎回来！请填写你的信息。</span>
                </div>
                <a-form :model="formState" name="basic" class="form-list" :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }" autocomplete="off">
                    <a-form-item label="手机" name="telephone" placeholder="请输入手机"
                        :rules="[{ required: true, message: '请输入您的手机！' }]">
                        <a-input v-model:value="formState.telephone" style="height: 42px;" />
                    </a-form-item>
                    <a-form-item label="密码" name="password" placeholder="请输入密码"
                        :rules="[{ required: true, message: '请输入密码！' }]">
                        <a-input-password v-model:value="formState.password" style="height: 42px;" />
                    </a-form-item>

                    <a-form-item>
                        <a-button type="primary" html-type="submit" @click="submitLogin"
                            style="width: 100%; height: 42px; margin-top: 20px;">登录</a-button>
                    </a-form-item>
                </a-form>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
    import { reactive, onMounted } from "vue";
    import { useRouter, useRoute } from "vue-router";
    import loginApi from "@/api/auth/auth.api";
    import { addCdnUrl } from '@/common/utils';


    const myImg = addCdnUrl(new URL(`./img/loginLeft.png`, import.meta.url).href);
    const logo = addCdnUrl(new URL(`./img/logo.png`, import.meta.url).href);
    const logoName = addCdnUrl(new URL(`./img/logoName.png`, import.meta.url).href);


    const router = useRouter();
    const route = useRoute();


    interface FormState {
        telephone: string;
        password: string;
    }

    const formState = reactive < FormState > ({
        telephone: "",
        password: "",
    });



    onMounted(() => {
        // document.title = "登录";

        //你已经登录
        if (localStorage.getItem("token")) {
            window.location.href = route.query.goto ? `${route.query.goto}` : "/workbench";
            // window.location.href = "/workbench";
        }
    });

    const submitLogin = async () => {
        let result = await loginApi.loginV2({
            ...formState
        });
        console.log('result', result);


        if (+result.code == 200) {
            localStorage.setItem("token", result.data.token);
            localStorage.setItem("telephone", result.data.telephone);
            window.location.href = route.query.goto ? `${route.query.goto}` : "/workbench";
        }
    };
</script>

<style scoped lang="less">
    @import 'amber-design-pro/dist/styles/default.less';

    ::v-deep.form-list {
        .ant-form-item-label {
            font-size: @font-2;
            padding-bottom: 0px !important;
        }

        .ant-form-item .ant-form-item-label>label {
            font-size: @font-2 !important;

        }

        .ant-input {
            height: unset;
            font-size: @font-2;

            &:hover {
                border-color: @blue-7
            }
        }

        .ant-input-affix-wrapper {
            /* background: @white !important;
            color: @black !important;
            border-color: @gray-5; */

            &:hover {
                border-color: @blue-7
            }

        }

        .ant-form-item-with-help .ant-form-item-explain {
            height: auto !important;

        }

        .ant-form-item-explain-error {
            font-size: @font-2 !important;
        }

        .ant-form-item {
            height: 92px !important;
        }
    }

    .login-container {
        display: flex;
        width: 100%;
        height: 100%;
        background-color: @gray-12;
        color: @gray-5;

        .login-left {
            width: 55%;
            max-width: 1200px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .login-right {
            flex: 1;
            min-width: 500px;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 100%;
            overflow: hidden;

            .system-name {
                display: flex;
                align-items: center;
                gap: 8px;

                img {
                    width: 32px;
                    height: 32px;
                }

                span {
                    font-size: large;
                    font-weight: bold;
                }

            }

            /* 
            img {
                width: 147px;
                height: 40px;
                position: absolute;
                top: 2%;
                right: 4%;
            } */

            .login-form {
                width: 350px;

                .title {
                    margin-top: 80px;
                    text-align: left;
                    margin-bottom: 12px;

                    span {
                        font-size: 32px;
                        font-weight: 900;
                        color: @gray-5;
                        text-shadow: 0px 12px 24px rgba(39, 91, 136, 0.08);
                    }
                }

                .desc {
                    margin-bottom: 32px;

                    span {
                        color: @gray-6;
                    }
                }
            }
        }
    }
</style>