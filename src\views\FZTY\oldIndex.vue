<script setup>
    import { ref, onMounted, computed, createVNode } from 'vue'
    import { Modal, message } from 'ant-design-vue'
    import {
        ExclamationCircleOutlined,
        UploadOutlined
    } from '@ant-design/icons-vue'
    import { tableMixin } from '@/mixins/table';
    import breadcrumb from '../home/<USER>/breadcrumb.vue';
    import fileApi from '@/api/file/file.api'
    import importModel from '@/components/importModel.vue'
    import axios from 'axios'
    const visible = ref(false)
    const type = ref('add')
    const filters = ref()
    const searchName = ref('')
    const columns = ref([
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align: 'center',
            width: 60,
            fixed: 'left'
        },
        {
            title: '文件全名称',
            dataIndex: 'fileName',
            key: 'fileName',
            width: 180,
            ellipsis: true
        },
        // {
        //     title: '分域',
        //     dataIndex: 'domainName',
        //     key: 'domainName',
        //     width: 120,
        //     ellipsis: true
        // },
        // {
        //     title: '系统',
        //     dataIndex: 'systemName',
        //     key: 'systemName',
        //     width: 120,
        //     ellipsis: true
        // },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 160,
            ellipsis: true
        },
        {
            title: '上传人',
            dataIndex: 'username',
            key: 'username',
            width: 120,
            ellipsis: true
        },
        {
            title: '大小',
            dataIndex: 'fileSizeFormatted',
            key: 'fileSizeFormatted',
            width: 100,
            align: 'center'
        },
        {
            title: '版本号',
            dataIndex: 'version',
            key: 'version',
            width: 80,
            align: 'center'
        },
        {
            title: '操作',
            dataIndex: 'operate',
            key: 'operate',
            width: 120,
            fixed: 'right',
            align: 'center'
        },
    ])
    const data = ref([])
    const headers = ref({})


    // 获取文件列表
    const getTableList = async () => {
        loading.value = true
        try {
            const params = { pagingSort: {}, sortList: [], name: searchName.value, step: 'FZTY' }
            fileApi.getFileList(params).then(res => {
                data.value = res.data.records
                pagination.value.total = res.total
            })
        } catch (error) {
            console.error('获取文件列表失败:', error)
            message.error('获取文件列表失败')
        } finally {
            loading.value = false
        }
    }

    //搜索
    // const searchTableList = async () => {
    //     if (!searchName.value.trim()) {
    //         // 如果搜索框为空，则获取所有列表
    //         getTableList()
    //         return
    //     }

    //     loading.value = true
    //     try {
    //         // 注意：GET请求通常使用URL参数而不是form-data
    //         // 但如果后端确实需要通过form-data接收GET请求参数
    //         // 则可以构造一个包含name参数的对象传给searchFile
    //         const params = { name: searchName.value,step:'FZTY' }
    //         const result = await fileApi.searchFile(params)

    //         if (result) {
    //             data.value = result
    //             pagination.value.total = result.total || result.length
    //         } else {
    //             message.error('搜索失败：无数据返回')
    //         }
    //     } catch (error) {
    //         console.error('搜索文件失败:', error)
    //         message.error('搜索失败：' + (error.message || '未知错误'))
    //     } finally {
    //         loading.value = false
    //     }
    // }


    // 下载文件
    const download = async (file) => {
        try {
            message.success(`开始下载文件：${file.name}`)
            // 使用 axios 进行文件下载，以便处理响应头
            const response = await axios.get(`/api/files/download/${file.id}`, {
                responseType: 'blob' // 设置响应类型为 blob
            })

            // 创建 Blob URL
            const blob = new Blob([response.data], {
                type: response.headers['content-type']
            })
            const url = window.URL.createObjectURL(blob)

            // 创建一个隐藏的a标签来处理下载
            const link = document.createElement('a')
            link.href = url
            // 使用 encodeURIComponent 对文件名进行编码
            link.download = decodeURIComponent(file.name)
            document.body.appendChild(link)
            link.click()

            // 清理资源
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
        } catch (error) {
            console.error('下载文件错误:', error)
            message.error(`下载失败: ${error.message || '未知错误'}`)
        }
    }

    // 收藏/取消收藏文件
    const follow = async (record) => {
        if (!record || !record.id) {
            message.error('文件信息不完整')
            return
        }
        try {
            // 切换收藏状态 (1-收藏, 0-未收藏)
            const isImportant = record.isImportant ? 0 : 1
            const response = await fileApi.setImportant(record.id, isImportant)
            if (response.code == 200) {
                message.success('操作成功')
                getTableList()
            } else {
                message.error('操作失败')
            }
        } catch (error) {
            console.error('收藏操作失败:', error)
            message.error('操作失败')
        }
    }

    // 删除文件
    const deleteRow = (record) => {
        if (!record || !record.id) {
            message.error('文件信息不完整')
            return
        }
        const deleteFile = async () => {
            try {
                const response = await fileApi.deleteFile(record.id)

                if (response.code == 200) {
                    message.success('删除成功')
                    getTableList()
                } else {
                    message.error('删除失败')
                }
            } catch (error) {
                console.error('删除文件失败:', error)
                message.error('删除失败')
            }
        }

        Modal.confirm({
            title: '提示',
            icon: createVNode(ExclamationCircleOutlined),
            content: '是否确定删除所选项',
            onOk() {
                deleteFile();
            },
            onCancel() {
            },
        });
    }


    const filtersClick = (handler) => {
        switch (handler) {
            case 'search':
                getTableList()
                break
            case 'reset':
                searchName.value = ''
                getTableList()
                break
        }
    }

    // 文件上传
    const uploadChange = async ({ file }) => {
        if (file.status !== 'uploading') {
            const formData = new FormData()
            formData.append('file', file.originFileObj, file.name)
            formData.append('step', 'FZTY')

            try {
                const result = await fileApi.uploadFile(formData)

                if (result && result.id) {
                    message.success(`${file.name} 上传成功`)
                    getTableList()
                } else {
                    message.error(`${file.name} 上传失败: ${result?.message || '响应格式不正确'}`)
                }
            } catch (error) {
                console.error('上传文件失败:', error)
                message.error(`${file.name} 上传失败: ${error?.message || '网络或服务错误'}`)
            }
        }
    }

    const { pagination, tableHeight, change, loading } = tableMixin({
        getTableList
    });

    onMounted(() => {
        getTableList()
    })
</script>
<template>
    <div class="container">
        <breadcrumb></breadcrumb>
        <div class="page-content">
            <div class="page-filters">
                <a-button type="primary" @click="()=>{
                    visible=true
                }"> <upload-outlined></upload-outlined>上传文件</a-button>
                <a-space>
                    <label>文件检索：</label>
                    <a-input v-model:value="searchName" placeholder="请输入"></a-input>
                    <a-button type="primary" @click="filtersClick('search')">查询</a-button>
                    <a-button @click="filtersClick('reset')">重置</a-button>
                </a-space>
            </div>
            <ab-spin size="large" v-show="loading"></ab-spin>
            <div class="page-main">
                <a-table v-show="!loading" class="normal-table-component" :columns="columns"
                    :scroll="{ y: data.length === 0 ? 'auto' : tableHeight }" :data-source="data" :bodered="false"
                    :pagination="pagination" @change="change" :style="{ height: data.length === 0 ? '100%' : '100%' }">
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'index'">
                            {{ index + 1 }}
                        </template>
                        <template v-if="column.dataIndex === 'operate'">
                            <div class="operate">
                                <div class="operateIcon icon iconfont FCdownload-2-fill" @click="download(record)">
                                </div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateIcon icon iconfont FCstar-fill"
                                    :style="{ color: record.isImportant === 1 ? 'red' : '' }" @click="follow(record)">
                                </div>
                                <a-divider type="vertical" style="border-color:#333741" />
                                <div class="operateIcon icon iconfont FCdelete-bin-2-fill" @click="deleteRow(record)">
                                </div>
                            </div>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
    </div>
    <importModel :visible="visible" :step="'FZTY'" @close="()=>{
        visible = false;getTableList();
    }"></importModel>
</template>
<style lang="less" scoped>
    @import url('./style.less');

    .container {
        padding: 16px;
    }

    .page-content {
        height: calc(100% - 44px);
        background-color: rgba(12, 17, 29, 0.7);
        padding: 16px;
        border: 1px solid @gray-9;
        border-radius: 2px;
    }

    .operateIcon {
        color: @gray-6;
    }

    .page-main {
        height: calc(100% - 32px - 16px);
    }

    .page-filters {
        width: 100%;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
    }
</style>