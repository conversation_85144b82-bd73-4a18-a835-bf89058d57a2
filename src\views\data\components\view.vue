<script setup>
  import { ref, reactive, onMounted, computed, defineEmits } from 'vue';
  import { Chart } from '@antv/g2';
  import bussinessApi from '@/api/bussiness/bussiness.api';

  // 基本属性
  const state = reactive({
    loading: false,
    totalSystem: 0, // 系统总数
    subscribedSystem: 0, // 已接入系统总数
    totalTopic: 0, // 协议总数
    subscribeTopic: 0, // 已接入协议总数
  })

  const emit = defineEmits(['click'])
  let systemChart;
  let protocolChart;
  const systemRef = ref(null)
  const protocolRef = ref(null)

  const columns = ref([
    {
      title: '序号',
      key: 'index',
      width: 60,
      align: 'center',
      fixed: 'left',
      customRender: ({ text, record, index }) => {
        return index + 1;
      },
    },
    {
      title: '业务系统',
      dataIndex: 'systemName',
      key: 'systemName',
      width: 220,
      ellipsis: true,
    },
    {
      title: '已接入协议数',
      key: 'subscribedCount',
      width: 120,
      align: 'center',
      customRender: ({ record }) => {
        // API返回的是totalTopic和unsubscribe，计算已接入协议数
        return record.totalTopic - record.unsubscribe;
      }
    },
    {
      title: '未接入协议数',
      dataIndex: 'unsubscribe',
      key: 'unsubscribe',
      width: 120,
      align: 'center'
    },
    {
      title: '协议总数',
      dataIndex: 'totalTopic',
      key: 'totalTopic',
      width: 100,
      align: 'center'
    },
  ])
  const tableData = ref([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
  });

  function onChangeTable(page, pageSize) {
    pagination.value.current = page.current
    pagination.value.pageSize = page.pageSize
    getTableData()
  }

  /**
   * 渲染图形
   * @param container 
   */
  const initChartData = [
    { item: '1', count: 0 },
    { item: '2', count: 0 },
    { item: '暂无数据', count: 1 }]
  function renderChart(container) {
    const chart = new Chart({
      container,
      autoFit: true,
    });
    chart.coordinate({ type: 'theta', outerRadius: 0.75, innerRadius: 0.6 });

    // 初始化数据
    const data = [];

    // 声明可视化
    chart
      .interval() // 创建一个 Interval 标记
      .data(data) // 绑定数据
      .transform({ type: 'stackY' })
      .encode('y', 'count') // 编码 x 通道
      .encode('color', 'item') // 编码 y 通道
      // .style({
      //   stroke: 'black',
      //   strokeWidth: 2,
      //   inset: 0,
      //   margin: 2,
      // })
      .legend(false)
      .scale('color', {
        // 指定映射后的颜色
        range: ['#2970FF', '#17B26A', '#CCCCCC'],
      })
      .tooltip(false)
      .animate('enter', { type: 'waveIn', duration: 300 }); // 指定更新动画的时间

    // 渲染可视化
    chart.render();

    return chart;
  }

  /**
   * 更新图形数据
   * @param data 更新数据
   * @param type 图类型
   */
  function updateBarChart(data, type) {
    // 获得 Interval Mark
    let interval = protocolChart.getNodesByType('interval')[0];
    if (type === 'system') {
      interval = systemChart.getNodesByType('interval')[0];
      if (data) {
        let chartData = [
          { item: '接入数', count: data.subscribedSystem, },
          { item: '未接入数', count: data.totalSystem - data.subscribedSystem, }
        ]
        if (chartData.every(item => item.count === 0)) {
          chartData = [...initChartData]; // 占位数据
        }
        interval.data(chartData);
      } else {
        interval.data([...initChartData]);
      }
      // 重新渲染
      systemChart.render();
    } else {
      if (data) {
        let chartData = [
          { item: '接入数', count: data.subscribeTopic, },
          { item: '未接入数', count: data.totalTopic - data.subscribeTopic, }
        ]
        if (chartData.every(item => item.count === 0)) {
          chartData = [...initChartData]; // 占位数据
        }
        interval.data(chartData);
        // 重新渲染
        protocolChart.render();
      } else {
        interval.data([...initChartData]);
        protocolChart.render();
      }
    }
  }

  function getSystemOverview() {
    bussinessApi.getSystemOverview().then(res => {
      state.totalTopic = res.data.totalTopic
      state.subscribeTopic = res.data.subscribeTopic
      state.totalSystem = res.data.totalSystem
      state.subscribedSystem = res.data.subscribedSystem
      updateBarChart(res.data, 'system')
      updateBarChart(res.data, 'protocol')
    }).catch(err => {
      updateBarChart(false, 'system')
      updateBarChart(false, 'protocol')
    })
  }

  function getTableData() {
    const params = {
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize
    }
    bussinessApi.getSystemDetails(params).then(res => {
      if (res.data && res.data.records) {
        tableData.value = res.data.records
        pagination.value.total = res.data.total || 0
      } else {
        tableData.value = []
        pagination.value.total = 0
      }
    }).catch(err => {
      console.error('获取系统详情数据失败:', err)
      tableData.value = []
      pagination.value.total = 0
    })
  }

  function onClickSystem(record) {
    emit('click', record)
  }

  const statisticPercent = computed(() => {
    let system = 0
    let topic = 0
    if (state.totalSystem) {
      system = ((state.subscribedSystem / state.totalSystem) * 100).toFixed(1)
    }
    if (state.totalTopic) {
      topic = ((state.subscribeTopic / state.totalTopic) * 100).toFixed(1)
    }
    return { system, topic }
  })

  onMounted(() => {
    systemChart = renderChart(systemRef.value);
    protocolChart = renderChart(protocolRef.value);
    // 获取数据
    getSystemOverview()
    // 获取列表
    getTableData()
  })
</script>

<template>
  <div class="chart-warp">
    <div class="chart-item">
      <span class="chart-title">系统接入数</span>
      <div ref="systemRef" class="chart-container"></div>
      <div class="chart-legend">
        <span style="margin-bottom: 16px;">
          <span class="chart-legend-label">接入系统数</span>
          <span class="chart-legend-value">{{ statisticPercent.system }}%</span>
          <span>{{ state.subscribedSystem }}</span>
        </span>
        <span>
          <span class="chart-legend-label green">未接入系统数</span>
          <span class="chart-legend-value">
            {{ state.totalSystem == state.subscribedSystem ? 0 : (100 - statisticPercent.system).toFixed(1) }}%
          </span>
          <span>{{ state.totalSystem - state.subscribedSystem }}</span>
        </span>
      </div>
    </div>
    <div class="chart-item">
      <span class="chart-title">已接入协议数</span>
      <div ref="protocolRef" class="chart-container"></div>
      <div class="chart-legend">
        <span style="margin-bottom: 16px;">
          <span class="chart-legend-label">接入协议数</span>
          <span class="chart-legend-value">{{ statisticPercent.topic }}%</span>
          <span>{{ state.subscribeTopic }}</span>
        </span>
        <span>
          <span class="chart-legend-label green">未接入协议数</span>
          <span class="chart-legend-value">
            {{ state.totalTopic == state.subscribeTopic ? 0 : (100 - statisticPercent.topic).toFixed(1) }}%
          </span>
          <span>{{ state.totalTopic - state.subscribeTopic }}</span>
        </span>
      </div>
    </div>
  </div>
  <div class="detail">
    <a-table class="detail-table-component" :columns="columns" :data-source="tableData" :pagination="pagination"
      :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)" :scroll="{ y: tableHeight }"
      :style="{ height: tableData.length === 0 ? tableHeight + 'px' : '100%', overflow: 'auto' }"
      @change="onChangeTable">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key === 'systemName'">
          <a @click.stop="onClickSystem(record)">{{ text }}</a>
        </template>
        <template v-if="column.key === 'unsubscribe'">
          <span>{{ record.unsubscribe || 0 }}</span>
        </template>
        <template v-if="column.key === 'totalTopic'">
          <span>{{ record.totalTopic || 0 }}</span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="less">
  @import '../sytle.less';
</style>