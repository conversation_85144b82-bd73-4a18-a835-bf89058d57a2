const args = process.argv.slice(2);
import {
promisify
} from "util";
import {
  exec
} from "child_process";
import {
  Client
} from "node-scp";
import http from "http";
import fs from 'fs';

(async function () {
  // const util = require("util");
  // const exec = util.promisify(require("child_process").exec);
  // const { Client } = require("node-scp");
  // const fs = require("fs");
  // const ssh2Client = require("ssh2").Client;
  // const http = require("http");
  let branchName;
  let dirName = 'knowledgegraph'
  try {
    await promisify(exec)("git symbolic-ref --short HEAD")
      .then((res) => {
        const {
          stdout: branch,
          stderr: err
        } = res;
        if (branch) {
          branchName = branch.replace(/\s+/g, "");
        }
        if (err) {
          console.log(err);
        }
      })
      .catch((err) => {
        console.log(err);
      });

    const client = await Client({
      host: "*************",
      port: 22,
      // host: 'hzent.amberdata.cn',
      // port: 2722,
      username: "root",
      password: "Ckjb[[5/jzk",
      // privateKey: fs.readFileSync('./key.pem'),
      // passphrase: 'your key passphrase',
    });
    console.log("----------start upload----------");
    await client.uploadDir(
      `./dist/${dirName}/`,
      `/nfs-data/web-server-www-data-pvc-4eba5991-362b-4b21-b6dd-34a5557bd06b/${dirName}`
    );
    console.log("----------success upload----------");
    client.close(); // remember to close connection after you finish

    console.log("------------");
    const tokenRes = await httpRequest({
      host: "nacos.amberdata.cn",
      port: 8848,
      path: "/nacos/v1/auth/login?username=nacos&password=Dctm@1234",
      method: "POST",
    });
    const accessToken = tokenRes.accessToken;
    console.log(accessToken);

    const jsonPromise = new Promise((resolve, reject) => {
      let url = `http://nacos.amberdata.cn:8848/nacos/v1/cs/configs?dataId=micro-dev.yaml&group=DEFAULT_GROUP&namespaceId=81&tenant=81&show=all&accessToken=${accessToken}`;
      http
        .get(url, (res) => {
          let body = "";

          res.on("data", (chunk) => {
            body += chunk;
          });

          res.on("end", () => {
            try {
              let json = JSON.parse(body);
              resolve(json);
              // do something with JSON
            } catch (error) {
              console.error(error.message);
            }
          });
        })
        .on("error", (error) => {
          console.error(error.message);
        });
    });

    const json = await jsonPromise;

    let text = json.content;

    const hashValue = fs.readFileSync(
      `./dist/${dirName}/${branchName.replace(/\//g, "_")}.txt`
    );
    text = encodeURIComponent(text.replace(/hash:(.*)/, `hash: ${hashValue}`));
    //console.log(text)
    return;
  } catch (e) {
    console.log(e);
  }

  function httpRequest(params, postData) {
    return new Promise(function (resolve, reject) {
      var req = http.request(params, function (res) {
        // reject on bad status
        if (res.statusCode < 200 || res.statusCode >= 300) {
          return reject(new Error("statusCode=" + res.statusCode));
        }
        // cumulate data
        var body = [];
        res.on("data", function (chunk) {
          body.push(chunk);
        });
        // resolve on end
        res.on("end", function () {
          try {
            body = JSON.parse(Buffer.concat(body).toString());
          } catch (e) {
            reject(e);
          }
          resolve(body);
        });
      });
      // reject on request error
      req.on("error", function (err) {
        // This is not a "Second reject", just a different sort of failure
        reject(err);
      });
      if (postData) {
        req.write(postData);
      }
      // IMPORTANT
      req.end();
    });
  }
})();