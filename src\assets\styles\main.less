@import url('./common-params.less');
@import url(./global.var.less);

#app {
  height: 100%;
  font-weight: normal;
}

.db-metadata-normal-page {
  padding: 16px;
}


.main-layout {
  .ant-spin-spinning {
    width: 100%;
    height: 100%;
  }

  .ant-spin-nested-loading>div>.ant-spin {
    max-height: none;
  }

  .custom-spin .ant-spin-dot {
    position: relative;
    top: 45%;
  }

  .ant-modal-confirm .ant-modal-confirm-body {
    display: block;
  }
}



// 全屏弹窗
.full-modal {
  top: 0;
  min-width: 100%;
  padding-bottom: 0;

  .ant-modal-body {
    height: calc(100vh - 164px);
  }
}

// 表单样式
.container {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.page-filters {
  width: 100%;
}

.page-main {
  width: 100%;
  flex: 1;
}

.page-operates {
  margin-bottom: @margin-xl;
}

.page-title {
  font-size: 16px;
  margin-bottom: @margin-xl;
  font-weight: bold;
}

.page-main {
  height: 100%;

  .ant-spin-nested-loading,
  .ant-spin-container,
  .ant-table-wrapper {
    height: 100%;
  }

  .ant-table {
    height: calc(100% - 45px) !important;

    &.ant-table-empty {
      height: 100% !important;
    }
  }

  .ant-table-container>.ant-table-tbody {
    height: 100px;
  }

  .ant-table-thead>tr>th {
    padding: 12px;
  }

  .ant-table-cell {
    font-size: 12px;
    line-height: 20px;
    font-weight: 400;
    padding: 12px;
  }

  .ant-table-body {
    overflow-y: auto !important;
  }

  .ant-pagination {
    padding: 12px 8px;
    margin: 0;
    // border-top: 1px solid #F0F0F0;
  }

  .ant-table-empty .ant-table-container {
    height: 100%;
  }

  .ant-table-empty .ant-table-body {
    height: 100%;
  }

  .ant-table-empty .ant-table-body table {
    height: 100%;
  }

  .ant-table-empty .ant-table-placeholder {
    height: 100%;
  }

  .table-operate,
  .operate {
    .operateBlue {
      color: #2970FF;
      cursor: pointer;
    }

    .operateRed {
      color: #F5222D;
      cursor: pointer;
    }

    .operateDisabled {
      color: #d9d9d9;
      cursor: not-allowed;
    }
  }

  .operate {
    display: flex;
    align-items: center;
    justify-content: left;
    // gap: 10px;
    width: 100%;
  }
}

.icon-cursor {
  cursor: pointer;
}


.normal-table-component {
  background-color: rgba(#0c111d, 0.8) !important;
}

.base-font-size {
  font-size: 16px
}

.ant-picker .ant-picker-input >input[disabled],
.ant-picker.ant-picker-disabled,
.ant-select-disabled{
  background-color: @gray-11;
  color: @gray-7;
  border-color: @gray-9;
}
