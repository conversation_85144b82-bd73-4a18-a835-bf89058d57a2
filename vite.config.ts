import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import vue from '@vitejs/plugin-vue'
import { viteMockServe } from 'vite-plugin-mock'
import svgLoader from 'vite-svg-loader'

const proxyApi = 'http://127.0.0.1:8080/' //'https://hf219618ud74.vicp.fun/';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // css({ output: `assets/index-[hash].css` }), // 输出的css文件名带有hash
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false // css in js
        })
      ]
    }),
    // viteMockServe ({
    //   mockPath: 'src/mocks',
    // }),
    svgLoader({
      svgo: false,
      defaultImport: 'component'
    })
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: `@import "./node_modules/amber-design-pro/dist/styles/military-theme.less";`
      },
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    outDir: 'dist/knowledgegraph',
    rollupOptions: {
      output: {
        // 确保 chunk 文件只有一个，并且带有 hash
        manualChunks(id) {
          if (id.includes('node_modules')) {
            // 可以选择将第三方依赖也打包进主 bundle，或者分开
            // return 'vendors';
            return false; // 如果你想分开打包第三方依赖
          }
          return 'index'; // 所有其他代码都打包到 app.js
        },
        entryFileNames: `assets/[name]-[hash].js`, // 输出的js文件名带有hash
        chunkFileNames: `assets/[name]-[hash].js`, // 代码分割时生成的hash
        assetFileNames: `assets/[name]-[hash].[ext]` // 静态资源文件名带有hash
      },
    },
    // cssCodeSplit: false, // 禁止 CSS 代码分割，确保所有 CSS 都在一个文件中
  },
  server: {
    proxy: {
      '/api': {
        target: proxyApi,
        changeOrigin: true
      },
      '/organize': {
        target: proxyApi,
        changeOrigin: true
      },
      '/auth': {
        target: proxyApi,
        changeOrigin: true
      },
      '/user': {
        target: proxyApi,
        changeOrigin: true
      },
      '/file': {
        target: proxyApi,
        changeOrigin: true
      },
      '/filetransfer': {
        target: proxyApi,
        changeOrigin: true
      },
      '/fileTask': {
        target: proxyApi,
        changeOrigin: true
      },
    }
  }
})
