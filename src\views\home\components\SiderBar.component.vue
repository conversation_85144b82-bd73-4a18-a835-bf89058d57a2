<script setup>
  import { reactive, onMounted, watch, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { menuList, menuList_share } from '@/common/menuList'
  import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons-vue'
  import { useBreadcrumbStore } from '@/stores/breadcrumb'
  import { cloneDeep } from 'lodash-es'
  const breadcrumbStore = useBreadcrumbStore()
  const router = useRouter()
  const route = useRoute()
  const menuItems = ref([])
  const state = reactive({
    collapsed: false,
    selectedKeys: [menuItems.value[0]?.key],
    openKeys: [menuItems.value[0]?.key]
  })
  // 展开/收起
  const toggleCollapsed = () => {
    state.collapsed = !state.collapsed
  }

  function handleClick(e) {
    console.log(e.key)
    router.push({ path: e.key })
  }

  // 监听路由变化
  watch(
    () => route.path,
    (newPath, oldPath) => {
      updateMenuStatus();
      if (route.path.indexOf('query') != -1 || route.path.indexOf('Query') != -1) {
        menuItems.value = menuList_share
      } else {
        menuItems.value = menuList
      }
    },
    { immediate: true }
  )

  // 更新导航选中、展开状态
  function updateMenuStatus() {
    const { path, matched } = route;
    console.log('route', route);
    const staticList = route.matched.map(c => {
      return {
        name: c.meta.title,
        icon: c.meta.icon || ''
      }
    });
    console.log('staticList', staticList);

    breadcrumbStore.STATIC = staticList;
    console.log('breadcrumbStore.STATIC', breadcrumbStore.STATIC);
    if (path === '/') {
      state.selectedKeys = ['/dataDevelopment']
    } else {
      state.selectedKeys = [path]
    }
    state.openKeys = matched.map((item) => {
      return item.path
    })
  }
  onMounted(() => {
    updateMenuStatus()
  })

</script>

<template>
  <a-layout-sider width="196" v-model:collapsed="state.collapsed" :collapsedWidth="44" collapsible :trigger="null">
    <div class="studio-sidebar">
      <div class="studio-sidebar-menu">
        <a-menu mode="inline" v-model:selectedKeys="state.selectedKeys" v-model:openKeys="state.openKeys"
          :items="menuItems" :inlineIndent="14" @click="handleClick" theme="light">
        </a-menu>
      </div>

      <div :class="`studio-sidebar-action ${state.collapsed ? '' : 'expanded'}`">
        <MenuUnfoldOutlined v-if="state.collapsed" @click="toggleCollapsed" style="color: #fff; font-size: 16px" />
        <MenuFoldOutlined v-else @click="toggleCollapsed" style="color: #fff; font-size: 16px" />
      </div>
    </div>
  </a-layout-sider>
</template>

<style lang="less" scoped>
  @import './siderBar.less';
</style>