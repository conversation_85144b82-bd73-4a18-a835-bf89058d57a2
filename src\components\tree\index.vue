<template>
  <div class="tree-container">
    <a-tree v-if="props.treeData && treeData.length > 0" v-bind="{ ...props, ...attrs }" :key="addKey" class="base-tree"
      @update:expandedKeys="updateExpandedKeys" @select="handleSelect">
      <!-- 处理插槽 switcherIcon -->
      <template v-if="$slots.switcherIcon" #switcherIcon="{ switcherCls }">
        <slot name="switcherIcon" :switcherCls="switcherCls"></slot>
      </template>
      <!-- 处理插槽 title -->
      <template #title="{ dataRef }">
        <div class="base-tree-node node-content">
          <div class="base-tree-content">
            <slot name="icon" :key="dataRef[fieldNames.key || 'key']" :selected="dataRef.selected" :dataRef="dataRef">
            </slot>
            <!-- 判断是否为编辑状态 -->
            <template v-if="dataRef.isEditing">
              <a-input ref="editInputRef" v-model:value="dataRef[fieldNames.title || 'title']" @blur="saveNode(dataRef)"
                @pressEnter="saveNode(dataRef)" placeholder="请输入节点名称" />
            </template>
            <template v-else>
              <span class="base-tree-node-title" :title="dataRef[fieldNames.title || 'title']">{{
                dataRef[fieldNames.title || 'title'] }}</span>
            </template>
          </div>
          <slot name="nodeAction" :record="dataRef"></slot>
        </div>
      </template>

    </a-tree>
    <div v-else class="empty">
      <a-empty :image="image">
        <template #description>
          暂无数据
        </template>
      </a-empty>
    </div>
  </div>
</template>

<script setup>
import { defineEmits, toRefs, ref, defineProps, useAttrs, h, computed, nextTick } from 'vue';
import { darkImg, lightImg } from '@/assets/images/base64.js'

defineOptions({
  name: "abTree"
});
const editInputRef = ref()
const isComposing = ref(false);
const props = defineProps({
  treeData: { type: Array, required: true },
  expandedKeys: { type: Array, default: () => [] },
  selectedKeys: { type: Array, default: () => [] },
  fieldNames: {
    type: Object, default: () => {
      return {
        title: 'title',
        key: 'key',
        children: 'children'
      }
    }
  },
});
const image = computed(() => {
  if (attrs.type == 'dark') {
    return darkImg
  }
  return lightImg
})
const addKey = ref(0);
const attrs = useAttrs();
const emit = defineEmits(['saveNode', 'update:expandedKeys', 'update:selectedKeys', 'select'])
const { treeData, expandedKeys, selectedKeys, fieldNames } = toRefs(props);

// 新增节点
const addNode = (data) => {
  if (!data[fieldNames.value.children || 'children']) {
    data[fieldNames.value.children || 'children'] = [];
  }
  let newNode = {
    key: `${data[fieldNames.value.key || 'key']}-${data[fieldNames.value.children || 'children'].length}`,
    title: '',
    parent: data, // 指向父节点
    isEditing: true,
    operateType: 'addChildNode'
  };
  newNode[fieldNames.value.title || 'title'] = '';
  newNode[fieldNames.value.key || 'key'] = `${data[fieldNames.value.key || 'key']}-${data[fieldNames.value.children || 'children'].length}`,
    data[fieldNames.value.children || 'children'].push(newNode);
  ++addKey.value;
  // 自动展开父节点以显示新增项
  if (!expandedKeys.value.includes(data[fieldNames.value.key || 'key'])) {
    expandedKeys.value.push(data[fieldNames.value.key || 'key']);
    emit('update:expandedKeys', expandedKeys.value);
  }
};

// 编辑节点名称
const renameNode = (node) => {
  node.isEditing = true;
  node.operateType = 'renameNode';
};

// 保存节点
const saveNode = (node) => {
  if (!isComposing.value) {
    isComposing.value = true;
    node.isEditing = false;
    if (!node[fieldNames.value.title || 'title'].trim()) {
      removeNode(treeData.value, node[fieldNames.value.key || 'key']); // 删除空节点
    }
    emit('saveNode', node);
    setTimeout(() => {
      isComposing.value = false;
    }, 100);
  }
};

// 新增同级节点
const addSiblingNode = (node) => {
  const findParentAndAddSibling = (nodes, targetNode, parentNode = null) => { // 添加parentNode参数，默认为null
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i] === targetNode) {
        let siblingNode = {
          key: `${targetNode[fieldNames.value.key || 'key']}-sibling-${nodes.length}`,
          title: '',
          isEditing: true,
          operateType: 'addSiblingNode',
          siblingNode: targetNode,
          parent: parentNode // 父节点数据
        };
        siblingNode[fieldNames.value.title || 'title'] = '';
        nodes.splice(i + 1, 0, siblingNode); // 插入到当前节点之后
        nextTick(() => {
          editInputRef.value.focus();
        });
        ++addKey.value;
        return siblingNode; // 返回新创建的兄弟节点
      }
      if (nodes[i][fieldNames.value.children || 'children']) {
        const result = findParentAndAddSibling(nodes[i][fieldNames.value.children || 'children'], targetNode, nodes[i]);
        if (result) return result; // 如果找到了，则返回结果
      }
    }
    return null; // 没有找到合适位置时返回null
  };
  return findParentAndAddSibling(treeData.value, node); // 根据返回值判断操作是否成功及获取新兄弟节点信息
};
// 删除节点（当用户取消时）
const removeNode = (nodes, key) => {
  for (let i = nodes.length - 1; i >= 0; i--) {
    if (nodes[i][fieldNames.value.key || 'key'] === key) {
      nodes.splice(i, 1);
      return;
    }
    if (nodes[i][fieldNames.value.children || 'children']) {
      removeNode(nodes[i][fieldNames.value.children || 'children'], key);
    }
  }
};

// 更新展开的节点
const updateExpandedKeys = (newExpandedKeys) => {
  emit('update:expandedKeys', newExpandedKeys);
};

// 更新选中的节点
const updateSelectedKeys = (newSelectedKeys) => {
  emit('update:selectedKeys', newSelectedKeys);
};

// 处理选择事件
const handleSelect = (selectedKeys, e) => {
  updateSelectedKeys(selectedKeys);
  emit('select', selectedKeys, e);
};

defineExpose({
  addNode,
  addSiblingNode,
  renameNode,
  saveNode,
  updateExpandedKeys,
  updateSelectedKeys,
  handleSelect,
});
// },
// });
</script>

<style scoped lang="less">
// @import url('../../assets/styles/default.less');

.ant-tree-treenode,
.ant-tree-node-content-wrapper {
  width: 100% !important;
}

.ant-tree .ant-tree-switcher {
  line-height: 32px;
}

.tree-container {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.empty {
  padding: 0 12px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.base-tree {
  position: relative;
  padding: 0 12px;
  overflow: auto;
  overflow-x: hidden;
  flex: 1;

  div.ant-tree {
    color: #F5F5F6;

    .ant-tree-treenode {
      width: 100%;
      padding: 2px 0;

      &:hover {
        background: #333741;
      }

      .ant-tree-node-content-wrapper.ant-tree-node-selected {
        background-color: transparent;
      }

      .ant-tree-node-content-wrapper {
        &:hover {
          background: unset !important;
        }
      }
    }

    .ant-tree-treenode.ant-tree-treenode-selected {
      background: #333741;
    }
  }

  .tree-right-button {
    font-size: 18px;
    position: absolute;
    right: 12px;
    top: 5px;
    cursor: pointer;
    color: #F5F5F6;
  }

  .tree-node-icon {
    font-size: 14px;
    margin-right: 4px;
  }

  .base-tree-node {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    line-height: 32px;

    .base-tree-content {
      flex: 1;
      display: flex;
      align-items: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      gap: 4px;

      .base-tree-node-title{
        font-size: 16px;
      }
      input {
        width: 80%;
      }

    }
  }
}

.ant-tree .ant-tree-node-content-wrapper {
  border-radius: 1px;
}

.ant-tree .ant-tree-treenode {
  margin: 0 0 4px 0;
  padding: 0;
}

.ant-tree-treenode-selected,
.ant-tree-treenode:hover {
  background-color: #1F242F !important;
}

.base-tree-content {
  padding-right: 12px;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected,
.ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: transparent !important;
}

:deep(.ant-tree ){
  .ant-tree-switcher {
    max-width: 24px;
    width: 24px;
    line-height: 32px !important;
  }
  .ant-tree-treenode-selected{
    background: #1F242F;
  }
  .ant-tree-node-content-wrapper.ant-tree-node-selected, .ant-tree-checkbox+span.ant-tree-node-selected{
    background: transparent;
  }
}

:deep(.ant-empty) {
  .ant-empty-description {
    position: relative;
    top: -42px;
  }

  .ant-empty-footer {
    position: relative;
    top: -30px;
  }
}
</style>
