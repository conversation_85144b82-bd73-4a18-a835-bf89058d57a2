@import '@/assets/styles/global.var.less';

.sidebar-header {
  height: 32px;
  width: 100%;
}

:deep(.studio-sidebar) {
  display: flex;
  flex-direction: column;
  position: relative;
  // background-color: @containerBg;
  border-right: 1px solid @gray-9;
  overflow-y: auto;
  height: 100%;

  .studio-sidebar-menu {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .studio-sidebar-compute {
      position: relative;
      height: 300px;

      .mouse-move-horizontal {
        display: inline-block;
        width: 100%;
        border-top: 1px solid @gray-7;
        cursor: row-resize;
        position: relative;
        z-index: 1;

        &::after {
          content: '';
          width: 24px;
          height: 3px;
          position: absolute;
          display: block;
          top: 0;
          left: calc(50% - 12px);
          border-radius: 0 0 12px 12px;
          background: @gray-7;
        }
      }
    }
  }

  .studio-sidebar-action {
    display: flex;
    // height: 33px;
    align-items: center;
    background-color: @gray-12;
    // border-top: solid 1px @grey-8;
    position: relative;
    flex-direction: column-reverse;
    justify-content: center;

    >span {
      display: inline-block;
      width: 48px;
      line-height: 40px;
      cursor: pointer;
      text-align: center;
    }
  }

  .studio-sidebar-action.expanded {
    flex-direction: row;
    justify-content: space-between;
  }

  .ant-menu.ant-menu-inline {
    background-color: @gray-12;
  }
}

::v-deep {

  .ant-menu .ant-menu-item .ant-menu-item-icon,
  .ant-menu .ant-menu-submenu-title .ant-menu-item-icon,
  .ant-menu .ant-menu-item .anticon,
  .ant-menu .ant-menu-submenu-title .anticon {
    font-size: 20px;
  }
  .ant-menu.ant-menu-sub .ant-menu-item{
    padding-left: 46px !important;

  }
}