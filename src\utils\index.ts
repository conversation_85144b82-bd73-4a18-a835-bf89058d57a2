export function formatTimestamp(timestamp) {
    const date = new Date(timestamp); // 假设时间戳是秒，所以需要乘以1000转换为毫秒
    const year = date.getFullYear().toString(); // 获取年份的最后两位
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以需要+1，并用'0'填充
    const day = String(date.getDate()).padStart(2, '0'); // 日期用'0'填充
    const hours = String(date.getHours()).padStart(2, '0'); // 小时用'0'填充
    const minutes = String(date.getMinutes()).padStart(2, '0'); // 分钟用'0'填充
    const seconds = String(date.getSeconds()).padStart(2, '0'); // 秒用'0'填充

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

const Utils = {
    formatTimestamp
}

export default Utils;

