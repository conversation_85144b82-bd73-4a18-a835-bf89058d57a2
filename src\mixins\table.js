import { ref, onMounted } from 'vue';
import { useLoading } from '@/hooks/useLoading';

export function tableMixin(ctx) {
    const tableHeight = ref(0);
    const { loading, withLoading } = useLoading();
    const pagination = ref({
        current: 1,
        total: 0,
        pageSize: 30,
        pageSizeOptions: ['10', '20', '30','50', '100'],
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
    });
    const change = (pageInfo) => {
        pagination.value.current = pageInfo.current;
        pagination.value.pageSize = pageInfo.pageSize;
        ctx.getTableList();
    };
    onMounted(() => {
        window.onresize = () => {
            const empty =  document.getElementsByClassName('.ant-table-empty')[0];
            tableHeight.value = empty ? document.getElementsByClassName('page-main')[0].offsetHeight : document.getElementsByClassName('page-main')[0].offsetHeight - 92;
        };
        console.log('tableHeight document.getElementsByClassName',document.getElementsByClassName('page-main')[0].offsetHeight);
        
        tableHeight.value = document.getElementsByClassName('page-main')[0].offsetHeight - 92;
        console.log('tableHeighttableHeight',tableHeight);
        
        if (ctx.getTableList) {
            withLoading(ctx.getTableList);
        }
    });

    return {
        tableHeight,
        pagination,
        change,
        loading,
    };
}
