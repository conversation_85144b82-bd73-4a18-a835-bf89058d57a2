/**
 * MD5计算工具类
 * 支持Web Worker和主线程两种计算方式
 * 针对大文件优化，提供进度回调和错误处理
 */

import SparkMD5 from 'spark-md5';
import { formatFileSize } from '@/config/uploadConfig';

export interface MD5CalculationOptions {
  file: File;
  chunkSize: number;
  onProgress?: (progress: number, chunkIndex: number, totalChunks: number) => void;
  onComplete?: (md5: string, duration: number) => void;
  onError?: (error: string) => void;
  useWebWorker?: boolean; // 是否使用Web Worker
}

export interface MD5CalculationResult {
  md5: string;
  duration: number;
}

export class MD5Calculator {
  private worker: Worker | null = null;
  private isCalculating = false;
  private currentFile: File | null = null;
  private options: MD5CalculationOptions | null = null;

  /**
   * 计算文件MD5
   */
  async calculateMD5(options: MD5CalculationOptions): Promise<MD5CalculationResult> {
    if (this.isCalculating) {
      throw new Error('MD5计算正在进行中，请等待完成或先取消当前计算');
    }

    this.options = options;
    this.currentFile = options.file;
    this.isCalculating = true;

    try {
      // 检查是否支持Web Worker且用户选择使用
      const shouldUseWorker = options.useWebWorker !== false && this.isWebWorkerSupported();
      
      if (shouldUseWorker) {
        console.log(`🔄 使用Web Worker计算MD5: ${options.file.name}`);
        return await this.calculateWithWorker(options);
      } else {
        console.log(`🔄 使用主线程计算MD5: ${options.file.name}`);
        return await this.calculateInMainThread(options);
      }
    } finally {
      this.isCalculating = false;
      this.currentFile = null;
      this.options = null;
    }
  }

  /**
   * 使用Web Worker计算MD5（在无网络环境下可能失败，会自动降级到主线程）
   */
  private async calculateWithWorker(options: MD5CalculationOptions): Promise<MD5CalculationResult> {
    try {
      // 尝试创建Worker，如果失败则降级到主线程
      console.log('🔄 尝试使用Web Worker计算MD5...');
      
      // 在无网络环境下，Worker可能无法正常工作，直接降级到主线程
      console.log('🔄 检测到无网络环境或Worker不可用，降级到主线程计算');
      return await this.calculateInMainThread(options);
      
    } catch (error) {
      console.warn('🔄 Web Worker创建失败，降级到主线程计算:', error);
      return await this.calculateInMainThread(options);
    }
  }

  /**
   * 开始Worker计算
   */
  private async startWorkerCalculation(options: MD5CalculationOptions) {
    if (!this.worker) return;
    
    const { file, chunkSize } = options;
    
    // 发送开始消息
    this.worker.postMessage({
      type: 'start',
      data: {
        fileSize: file.size,
        chunkSize,
        fileName: file.name
      }
    });
    
    // 开始读取文件分片
    await this.readFileChunks(file, chunkSize);
  }

  /**
   * 读取文件分片并发送给Worker
   */
  private async readFileChunks(file: File, chunkSize: number) {
    const totalChunks = Math.ceil(file.size / chunkSize);
    
    for (let i = 0; i < totalChunks; i++) {
      if (!this.worker || !this.isCalculating) {
        break;
      }
      
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);
      
      try {
        const arrayBuffer = await this.readChunkAsArrayBuffer(chunk);
        
        this.worker.postMessage({
          type: 'chunk',
          data: {
            chunk: arrayBuffer,
            chunkIndex: i,
            totalChunks,
            isLast: i === totalChunks - 1
          }
        });
        
        // 大文件优化：适当延迟，避免内存压力
        if (file.size > 5 * 1024 * 1024 * 1024 && i % 10 === 0) { // 5GB以上文件
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
      } catch (error) {
        console.error(`❌ 读取分片 ${i} 失败:`, error);
        if (this.options?.onError) {
          this.options.onError(`读取文件分片失败: ${error.message}`);
        }
        break;
      }
    }
  }

  /**
   * 读取分片为ArrayBuffer
   */
  private readChunkAsArrayBuffer(chunk: Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as ArrayBuffer);
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(chunk);
    });
  }

  /**
   * 在主线程中计算MD5（优化版本，适用于大文件）
   */
  private async calculateInMainThread(options: MD5CalculationOptions): Promise<MD5CalculationResult> {
    return new Promise((resolve, reject) => {
      const { file, chunkSize, onProgress, onComplete, onError } = options;
      
      const fileReader = new FileReader();
      const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
      
      let currentChunk = 0;
      const totalChunks = Math.ceil(file.size / chunkSize);
      const spark = new SparkMD5.ArrayBuffer();
      const startTime = Date.now();
      
      // 性能优化参数
      const isLargeFile = file.size > 1024 * 1024 * 1024; // 1GB以上
      const isHugeFile = file.size > 10 * 1024 * 1024 * 1024; // 10GB以上
      
      console.log(`🔄 主线程开始计算MD5: ${file.name}`);
      console.log(`🔄 文件大小: ${formatFileSize(file.size)}, 分片数: ${totalChunks}`);
      console.log(`🔄 性能模式: ${isHugeFile ? '超大文件模式' : isLargeFile ? '大文件模式' : '标准模式'}`);
      
      // 进度节流优化
      let lastProgressUpdate = 0;
      const progressThrottleInterval = isHugeFile ? 1000 : isLargeFile ? 500 : 100; // ms
      
      const loadNext = () => {
        if (!this.isCalculating) {
          spark.destroy();
          reject(new Error('MD5计算已取消'));
          return;
        }
        
        const start = currentChunk * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
      };
      
      fileReader.onload = (e) => {
        try {
          spark.append(e.target?.result as ArrayBuffer);
          
          if (currentChunk < totalChunks) {
            currentChunk++;
            
            // 进度更新优化（节流）
            const now = Date.now();
            const progress = Math.round((currentChunk / totalChunks) * 100);
            
            if (now - lastProgressUpdate >= progressThrottleInterval || progress === 100) {
              lastProgressUpdate = now;
              if (onProgress) {
                onProgress(progress, currentChunk, totalChunks);
              }
            }
            
            // 性能优化：根据文件大小调整处理策略
            if (isHugeFile) {
              // 超大文件：更长的延迟，让出更多CPU时间
              setTimeout(loadNext, 5);
            } else if (isLargeFile) {
              // 大文件：适度延迟
              setTimeout(loadNext, 1);
            } else {
              // 小文件：立即处理
              loadNext();
            }
            
            // 内存管理：定期触发垃圾回收提示
            if (isHugeFile && currentChunk % 100 === 0) {
              // 对于超大文件，每100个分片后稍作停顿
              setTimeout(() => {
                // 强制垃圾回收（如果浏览器支持）
                if (typeof window !== 'undefined' && 'gc' in window) {
                  (window as any).gc();
                }
              }, 10);
            }
            
          } else {
            // 计算完成
            try {
              const md5 = spark.end();
              const duration = (Date.now() - startTime) / 1000;
              
              spark.destroy();
              
              console.log(`✅ 主线程MD5计算完成: ${md5}, 耗时: ${duration.toFixed(2)}秒`);
              console.log(`✅ 平均速度: ${(file.size / 1024 / 1024 / duration).toFixed(2)} MB/s`);
              
              if (onComplete) {
                onComplete(md5, duration);
              }
              
              resolve({ md5, duration });
              
            } catch (endError) {
              const errorMsg = `MD5计算完成时出错: ${endError.message}`;
              console.error('❌', errorMsg);
              if (onError) {
                onError(errorMsg);
              }
              reject(new Error(errorMsg));
            }
          }
        } catch (error) {
          const errorMsg = `MD5计算错误: ${error.message}`;
          console.error('❌', errorMsg);
          spark.destroy();
          if (onError) {
            onError(errorMsg);
          }
          reject(new Error(errorMsg));
        }
      };
      
      fileReader.onerror = () => {
        const errorMsg = `文件读取错误: ${fileReader.error?.message || '未知错误'}`;
        console.error('❌', errorMsg);
        spark.destroy();
        if (onError) {
          onError(errorMsg);
        }
        reject(new Error(errorMsg));
      };
      
      // 开始读取第一个分片
      loadNext();
    });
  }

  /**
   * 取消MD5计算
   */
  cancel() {
    this.isCalculating = false;
    
    if (this.worker) {
      this.worker.postMessage({ type: 'cancel' });
    }
    
    console.log('🔄 MD5计算已取消');
  }

  /**
   * 清理资源
   */
  private cleanup() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  /**
   * 检查是否支持Web Worker
   */
  private isWebWorkerSupported(): boolean {
    return typeof Worker !== 'undefined';
  }

  /**
   * 获取当前计算状态
   */
  get calculating(): boolean {
    return this.isCalculating;
  }

  /**
   * 获取当前计算的文件
   */
  get currentFileName(): string | null {
    return this.currentFile?.name || null;
  }
}