import { createRouter, createWebHistory } from 'vue-router'
import DataComponent from '../views/data/Index.vue'
import Home from '../views/home/<USER>'
import CHSJ from '../views/CHSJ/Index.vue'
import FPPG from '../views/FPPG/Index.vue'
import FZTY from '../views/FZTY/Index.vue'
import Login from '../views/login/Index.vue'
import Workbench from '../views/workbench/Index.vue'
import comprehensiveQuery from '../views/comprehensiveQuery/Index.vue'
import queryCHSJ from '../views/queryCHSJ/Index.vue'
import otherQuery from '../views/otherQuery/Index.vue'
import queryFZTY from '../views/queryFZTY/Index.vue'
import queryFPPG from '../views/queryFPPG/Index.vue'
import querySSDK from '../views/querySSDK/Index.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login
    },
    {
      path: "/workbench",
      name: "workbench",
      meta: { title: "工作台" },
      component: Workbench,
    },
    {
      path: '/',
      redirect: "/workbench",
    },
    {
      path: '/CHSJ',
      name: 'CHSJ',
      meta: {
        title: "筹划设计阶段",//筹划设计阶段
        icon: 'FCsuanfamoxingguanli',
      },
      component: Home,
      children: [
        {
          path: 'index', // 修正路径
          name: 'CHSJIndex',
          component: CHSJ,
          meta: {
            title: '筹划设计阶段'//筹划设计阶段
          }

        }
      ]
    },
    {
      path: '/FPPG',
      name: 'FPPG',
      meta: {
        title: "复盘评估阶段",
        icon: 'FCbookmark',
      },
      component: Home,
      children: [
        {
          path: 'index', // 修正路径
          name: 'FPPGIndex',
          component: FPPG,
          meta: {
            title: '复盘评估阶段'
          }

        }
      ]
    },
    {
      path: '/comprehensiveQuery',
      name: 'comprehensiveQuery',
      meta: {
        title: "综合查询",
        icon: 'FCmenu-search',
      },
      component: Home,
      children: [
        {
          path: 'index', // 修正路径
          name: 'comprehensiveQueryIndex',
          component: comprehensiveQuery,
          meta: {
            title: '综合查询'
          }

        }
      ]
    },
    {
      path: '/query',
      name: 'query',
      meta: {
        title: "查询方式1",
        icon: 'FCmobanguanli',
      },
      component: Home,
      children: [
        {
          path: 'queryCHSJ', // 修正路径
          name: 'queryCHSJ',
          component: queryCHSJ,
          meta: {
            title: '筹划设计阶段'//筹划设计阶段
          }
        },
        {
          path: 'queryFZTY', // 修正路径
          name: 'queryFZTY',
          component: queryFZTY,
          meta: {
            title: '仿真推演阶段'//仿真推演阶段
          }
        },
        {
          path: 'queryFPPG', // 修正路径
          name: 'queryFPPG',
          component: queryFPPG,
          meta: {
            title: '复盘评估阶段'
          }
        },
        {
          path: 'querySSDK', // 修正路径
          name: 'querySSDK',
          component: querySSDK,
          meta: {
            title: 'SSDK阶段'//数实对抗阶段
          }

        }
      ]
    },
    {
      path: '/FZTY',
      name: 'FZTY',
      meta: {
        title: "仿真推演阶段",//仿真推演阶段
        icon: 'FCtimeline-view',
      },
      component: Home,
      children: [
        {
          path: 'index', // 修正路径
          name: 'FZTYIndex',
          component: FZTY,
          meta: {
            title: '仿真推演阶段'//仿真推演阶段
          }

        }
      ]
    },
    {
      path: '/otherQuery',
      name: 'otherQuery',
      meta: {
        title: "查询方式2",
        icon: 'FCbookmark',
      },
      component: Home,
      children: [
        {
          path: 'index', // 修正路径
          name: 'otherQueryIndex',
          component: otherQuery,
          meta: {
            title: '查询方式2'
          }

        }
      ]
    },
    {
      path: '/data',
      name: 'home',
      component: Home,
      meta: {
        title: "SSDK阶段",//数实对抗阶段
        icon: 'FCmobanguanli',
      },
      children: [
        {
          path: 'data', // 修正路径
          name: 'data',
          meta: {
            title: "实时数据",
          },
          component: () => import('@/views/data/index.vue'),
        },
        {
          path: 'offlineFile', // 修正路径
          name: 'offlineFile',
          meta: {
            title: "离线数据",
          },
          component: () => import('@/views/offlineFile/index.vue'),
        },
        {
          path: 'meteorologicalData', // 修正路径
          name: 'meteorologicalData',
          meta: {
            title: "人工填报气象数据",
          },
          component: () => import('@/views/meteorologicalData/index.vue'),
        }
      ]
    },

  ]
});

export default router
