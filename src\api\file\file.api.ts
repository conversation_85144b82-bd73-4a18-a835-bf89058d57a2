import request from '@/common/request'
import { FileApiUrl } from './file.url'

class FileApi {
  /**
   * 获取文件列表
   * @returns 
   */
  getFileList = (data: any) => {
    // 构建符合新格式的参数
    const params: Record<string, any> = {
      filePath: data.filePath || "",
      currentPage: data.pagingSort?.currentPage || 1,
      pageCount: data.pagingSort?.pageSize || 10,
      fileType: data.fileType || "0", // 固定值
      isDir: data.isDir || 0,
      step: data.step || "",
      name: data.name || "",
      domainId: data.domainId || "",
      systemId: data.systemId || "",
      username: data.username
    }
    
    // 如果有时间范围参数，则添加
    if (data.startTime && data.endTime) {
      params['startTime'] = data.startTime
      params['endTime'] = data.endTime
    }
    
    // 如果有机构ID，则添加
    if (data.organizeId) {
      params['organizeId'] = data.organizeId
    }
    
    return request.get(FileApiUrl.fileList, params, {
      headers: {
        'Authorization': localStorage.getItem("token")
      }
    })
  }

  /**
   * 上传文件
   * @param data 
   * @returns 
   */
  uploadFile = (data: any) => request.post(FileApiUrl.filesUpload, data)

  /**
   * 检验文件是否重复
   * @param data 
   * @returns 
   */
  checkFileExist = (data: any) => request.post(FileApiUrl.checkFileExist, data)

  /**
   * 删除文件
   * @param id 文件id
   * @returns 
   */
  deleteFile = (id: any) => request.delete(FileApiUrl.fileDelete.replace('{id}', id), {
    headers: {
      'Authorization': localStorage.getItem("token")
    }
  })

  /**
   * 设置文件重要性
   * @param id 文件id
   * @param isImportant 是否重要
   * @returns 
   */
  setImportant = (id: any, isImportant: any) => request.put(FileApiUrl.setImportant.replace('{userFileId}', id).replace('{status}', isImportant))

  /**
   * 下载文件
   * @param id 文件id
   * @returns 
   */
  downloadFile = (id: any) => request.get(FileApiUrl.fileDownload.replace('{id}', id))

  /**
   * 
   * @param name 搜索文件
   * @returns 
   */
  searchFile = (params: any) => request.get(FileApiUrl.fileSearch, params)

  /**
   * 创建文件夹
   * @param data 
   * @returns 
   */
  createFold = (data: any) => request.post(FileApiUrl.createFold, data)

  /**
   * 获取文件夹树
   * @param params 
   * @returns 
   */
  fileTree = (params: any) => {
    // 构建符合新格式的参数
    const requestParams: Record<string, any> = {
      filePath: params.filePath || "/",
      currentPage: params.currentPage || 1,
      pageCount: params.pageCount || 10,
      fileType: "2", // 固定值
      isDir: params.isDir || 0,
      step: params.step || "CHSJ",
      name: params.name || "",
      domainId: params.domainId || "",
      systemId: params.systemId || ""
    }
    
    return request.get(FileApiUrl.fileTree, requestParams)
  }

  /**
   * 重命名文件夹
   * @param data 
   * @returns 
   */
  renameFile = (data: any) => request.post(FileApiUrl.renameFile, data, {
    headers: {
      'token': localStorage.getItem("token")
    }
  })

  /**
   * 批量删除文件
   * @param data 
   * @returns 
   */
  batchDeleteFile = (data: any) => request.post(FileApiUrl.batchDeleteFile, data)

  /**
   * 上传文件夹
   * @param data 
   * @returns 
   */
  // uploadFolder = (data: any) => request.post(FileApiUrl.renameFile, data)

  /**
   * 批量下载
   * @param params 
   * @returns 
   */
  batchDownloadFile = (params: any) => request.get(FileApiUrl.batchDownloadFile, params)

  /**
   * 获取内存
   * @returns 
   */
  getStorage = () => request.get(FileApiUrl.getStorage)

  /**
   * 保存上传文件
   * @param data 
   * @returns 
   */
  saveFile = (data: any) => {
    console.log('保存文件API调用，数据:', data);
    return request.post(FileApiUrl.saveFile, data);
  }

  listByBatchId = (data: any) => request.post(FileApiUrl.listByBatchId, data)

  batchCancel = (data: any) => request.post(FileApiUrl.batchCancel, data)

  batchDelete = (data: any) => request.post(FileApiUrl.batchDelete, data)

  batchPause = (data: any) => request.post(FileApiUrl.batchPause, data)

  batchUpload = (data: any) => request.post(FileApiUrl.batchUpload, data)

  /**
   * 查询已经上传的分片
   * @param data 
   * @returns 
   */
  getUploadedChunks = (data: any) => request.post(FileApiUrl.getUploadedChunks, data)

  /**
   * 批量设置任务成功状态
   * @param taskIds 任务id数组
   * @returns 
   */
  batchSuccess = (taskIds: number[]) => request.post(FileApiUrl.batchSuccess, taskIds, {
    headers: {
      'token': localStorage.getItem("token")
    }
  })


}

const fileApi = new FileApi()
export default fileApi
