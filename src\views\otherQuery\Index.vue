<script lang="ts" setup>
  import { ref, reactive, defineProps, onMounted, watch, nextTick } from 'vue';
  import { MoreOutlined } from '@ant-design/icons-vue';
  import { message, Modal } from 'ant-design-vue'
  import abTree from '@/components/tree/index.vue';
  import bussinessApi from '@/api/bussiness/bussiness.api';
  import OrganizeApi from '@/api/organize/organize.api';
  import dataApi from '@/api/data/data.api';
  import ProtocolApi from '@/api/protocol/protocol.api';
  import breadcrumb from '@/views/home/<USER>/breadcrumb.vue';
  import fileApi from '@/api/file/file.api'
  import { tableMixin } from '@/mixins/table';
  import { DEFAULT_COLUMNS } from './common/column.ts'

  import axios from 'axios'
  enum contentEnum {
    view = 'VIEW', // 概览
    list = 'LIST', // 主题列表
    detail = 'DETAIL', // 主题对应数据
  }
  const dataType = ref('lx')
  const createdTime = ref(null)
  const createdTimeSSWB = ref(null)
  const createdTimeSSWBDwd = ref(null)
  const searchForm = reactive({
    step: undefined,
    domainId: undefined,
    organizeId: undefined,
    username: undefined,
    startTime: undefined,
    systemId: undefined,
    endTime: undefined,
    name: undefined
  })
  const search = ref({})
  // 基本属性
  const state: any = reactive({
    keywords: undefined, // 检索关键字
    // loading: false,
    // id: null,
    // tableName: undefined,
    // type: 1 // 1：域 2: 系统
  })
  const dataStatistics = ref([{ tableName: '-', total: '-' }])
  const activeKey = ref('1');
  const showActiveKey = ref('1');
  const data = ref([])
  const treeRef = ref(null);
  const treeState: any = reactive({
    expandedKeys: [],
    selectedKeys: [],
    list: [],
    currentNode: {}
  })
  // 组织机构树
  const treeStateOrg: any = reactive({
    expandedKeys: [],
    selectedKeys: [],
    list: [
    ]
  })
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,

  });
  const paginationsswb = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,

  });
  const dwdPagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条`,
  });
  const treeFieldNames = {
    children: 'systems',
    title: 'name',
    key: 'key',
  }
  const columns = ref(DEFAULT_COLUMNS)
  const tableData = ref([])//原始库数据
  const compilationTableData = ref([])//整编库数据
  /**
   * 获取嵌套数组的key
   * @param arr 
   */
  function getTreeNodeKey(arr: any[]) {
    const result = []
    for (const item of arr) {
      if (item.children && Array.isArray(item.children)) {
        const arr: any = getTreeNodeKey(item.children)
        result.push(...arr)
      }
      result.push(item.id);
    }
    return result
  }

  // 监听左侧数据树选择变化
  function selectTreeNode(selectedKeys: any, e: any) {
    console.log("🚀 , e:", e.node, e.node.id);
    const { id, type, business, name } = e.node;
    if (e.node.type == 'other' && e.node.name == '离线数据') {
      searchForm.systemId = e.node.systemId;
    }
    treeState.currentNode = e.node.dataRef;
    console.log(id, type, business, name)
  }


  /**
   * 加载树目录数据
   * @param treeNode 
   */
  const onLoadTreeData = (treeNode: any) => {
    return new Promise < void> (async resolve => {
      if (treeNode.dataRef.systems || treeNode.dataRef.isLeaf) {
        resolve();
        return;
      }
      if (!treeNode.dataRef.isLeaf) {
        const data: any = await ProtocolApi.getProtocolListBySystem(treeNode.dataRef.id)
        treeNode.dataRef.systems = [
          {
            id: treeNode.dataRef.key + 'SSBW',
            key: treeNode.dataRef.key + 'SSBW',
            name: 'SSBW数据',
            type: 'other',
            systemId: treeNode.dataRef.id,
            systems: [],
          },
          {
            id: treeNode.dataRef.key + 'LX',
            key: treeNode.dataRef.key + 'LX',
            name: '离线数据',
            type: 'other',
            systemId: treeNode.dataRef.id,
            isLeaf: true,
          }];

        console.log('treeState.selectedKeys', treeState.selectedKeys)
        if (treeState.selectedKeys.length == 0) {
          treeState.selectedKeys = [treeNode.dataRef.key + 'LX'];
          treeState.currentNode = {
            id: treeNode.dataRef.key + 'LX',
            key: treeNode.dataRef.key + 'LX',
            name: '离线数据',
            type: 'other',
            systemId: treeNode.dataRef.id,
            isLeaf: true,
          }
        }
        if (data.data && data.data.length > 0) {
          for (const item of data.data) {
            treeNode.dataRef.systems[0].systems.push({
              ...item,
              type: contentEnum.detail,
              isLeaf: true,
            })
          }
        } else {
          treeNode.dataRef.isLeaf = true
        }
        resolve();
      }
    })
  }

  async function getTreeData() {
    treeState.list = []
    const res = await bussinessApi.getBussinessSystems()
    const result = res.data;
    if (result && Array.isArray(result)) {
      for (const area of result) {
        let areaObj = {
          ...area,
          type: contentEnum.view,
          key: area.id + area.name,
          region: area.id
        }
        areaObj.systems = area.systems.map((item: any) => {
          return { ...item, type: contentEnum.list, key: item.id + item.name, isLeaf: false, region: area.id, business: item.id }
        })
        treeState.list.push(areaObj)
      }
    }
    console.log('treeState.list', treeState.list);

    if (treeState.expandedKeys.length == 0) {
      treeState.expandedKeys = [treeState.list[0].key, treeState.list[0].systems[0].key];
    }
  }
  async function getOrgTreeData() {
    const res = await OrganizeApi.getOrgTree();
    console.log('organizeList', res);
    treeStateOrg.list = res.data;
    console.log('treeStateOrg.list', treeStateOrg.list);

    if (treeStateOrg.expandedKeys.length == 0) {
      treeStateOrg.expandedKeys = [treeStateOrg.list[0].id];
    }
    if (treeStateOrg.selectedKeys.length == 0) {
      treeStateOrg.selectedKeys = [treeStateOrg.list[0].id];
    }
    getTableList();
  }
  function onSubcribe(record: any, status: number) {
    console.log('record', record);
    const params = {
      systemId: record.type == 'LIST' ? record.id : '',
      domainId: record.type == 'VIEW' ? record.id : '',
      protocolId: record.type == 'DETAIL' ? record.id : '',
      status: status
    }
    console.log('params', params);

    ProtocolApi.subscribeProtocol(params).then(res => {
      console.log('订阅参数：', params)
      // state.id = undefined
      message.success('操作成功')
    })
  }

  watch(() => treeState.expandedKeys, (val => {
    sessionStorage.setItem('treeState.expandedKeys', JSON.stringify(val))
  }), { deep: true })
  watch(() => treeState.selectedKeys, (val => {
    sessionStorage.setItem('treeState.selectedKeys', JSON.stringify(val));
  }), { deep: true })
  watch(() => treeState.currentNode, (val => {
    sessionStorage.setItem('treeState.currentNode', JSON.stringify(val));
    console.log('treeState.currentNodetreeState.currentNode', treeState.currentNode);

    getTableList()
  }), { deep: true })
  watch(() => treeStateOrg.expandedKeys, (val => {
    sessionStorage.setItem('treeStateOrg.expandedKeys', JSON.stringify(val))
  }), { deep: true })
  watch(() => treeStateOrg.selectedKeys, (val => {
    sessionStorage.setItem('treeStateOrg.selectedKeys', JSON.stringify(val));
    getTableList()
  }), { deep: true })
  watch(() => searchForm.systemId, (val => {
    sessionStorage.setItem('searchForm.systemId', val || '')
  }), { deep: true })
  watch(() => activeKey.value, (val => {
    if (val == '1') {
      getTreeData()
    } else {
      getOrgTreeData()
    }
  }), { deep: true })
  onMounted(() => {
    getTreeData()
    const cache = {
      // content: sessionStorage.getItem('state.content'),
      expandedKeys: sessionStorage.getItem('treeState.expandedKeys'),
      selectedKeys: sessionStorage.getItem('treeState.selectedKeys'),
      // tableName: sessionStorage.getItem('state.tableName'),
    }
    // 初始选择预览
    if (cache.expandedKeys) {
      treeState.expandedKeys = JSON.parse(cache.expandedKeys)
    }
    if (cache.selectedKeys) {
      treeState.selectedKeys = JSON.parse(cache.selectedKeys);
    }
    // if (cache.content) {
    //   state.content = cache.content
    // }
    // if (cache.tableName) {
    //   state.tableName = cache.tableName
    // }
    if (sessionStorage.getItem('treeStateOrg.expandedKeys')) {
      treeStateOrg.expandedKeys = JSON.parse(sessionStorage.getItem('treeStateOrg.expandedKeys'))
    }
    if (sessionStorage.getItem('treeStateOrg.selectedKeys')) {
      treeStateOrg.selectedKeys = JSON.parse(sessionStorage.getItem('treeStateOrg.selectedKeys'))
    }
    if (sessionStorage.getItem('treeState.currentNode')) {
      console.log('treeState.currentNode', treeState.currentNode);

      treeState.currentNode = JSON.parse(sessionStorage.getItem('treeState.currentNode'))
      console.log('treeState.currentNode1', treeState.currentNode);
    }
  })
  const filtersClick = (handler: string) => {
    switch (handler) {
      case 'search':
        getTableList()
        break
      case 'reset':
        searchForm.username = undefined; // 清空用户名
        searchForm.startTime = undefined; // 清空开始时间
        searchForm.endTime = undefined; // 清空开始时间
        searchForm.name = undefined; // 清空开始时间
        createdTime.value = null
        getTableList()
        break
    }
  }
  const filtersClickSSBW = (handler: string, type?: string) => {
    switch (handler) {
      case 'search':
        getSSBWData()
        break
      case 'reset':
        if (type) {
          createdTimeSSWBDwd.value = null
        } else {
          createdTimeSSWB.value = null
        }
        getSSBWData()
        break
    }
  }

  const getSSBWData = async () => {
    if (!pollingTimer) return;
    dataType.value = 'ssbw';
    const params = {
      pagingSort: {
        currentPage: paginationsswb.value.current,
        pageSize: paginationsswb.value.pageSize,
      },
      tableName: treeState.currentNode.name,
      startTime: undefined,
      endTime: undefined,
    }

    if (createdTimeSSWB.value) {
      params.startTime = createdTimeSSWB.value[0]
      params.endTime = createdTimeSSWB.value[1]
    }
    //获取原始库数据
    dataApi.getSSBWPageTable(params).then(res => {
      console.log('resresres', res);

      const { pageData } = res.data
      const [zl, zlmc] = treeState.currentNode.name.split('_')
      dataStatistics.value = [{ zlmc: zlmc, zl: zl, total: pageData?.total }]
      const arr = [{
        title: '序号',
        key: 'index',
        width: 80,
        customRender: ({ text, record, index }) => {
          return index + 1;
        },
      }]
      for (const col of res.data.columns) {
        arr.push({
          title: col.comment,
          key: col.columnName,
          ellipsis: true,
          dataIndex: col.columnName
        })
      }
      columns.value = arr
      tableData.value = pageData.records // 原始库
      paginationsswb.value.total = pageData.total

      // 获取整编库数据，使用dwd_前缀
      const dwdParams = {
        pagingSort: {
          currentPage: dwdPagination.value.current,
          pageSize: dwdPagination.value.pageSize,
        },
        tableName: `dwd_${treeState.currentNode.name}`,
        startTime: undefined,
        endTime: undefined,
      }
      if (createdTimeSSWBDwd.value) {
        params.startTime = createdTimeSSWBDwd.value[0]
        params.endTime = createdTimeSSWBDwd.value[1]
      }
      dataApi.getSSBWPageTable(dwdParams).then(dwdRes => {
        console.log('dwdRes', dwdRes);

        if (dwdRes && dwdRes.data && dwdRes.data.pageData) {
          compilationTableData.value = dwdRes.data.pageData.records // 整编库
          dwdPagination.value.total = dwdRes.data.pageData.total
        } else {
          compilationTableData.value = [] // 如果没有数据则设为空数组
        }
      }).catch(err => {
        console.error("获取整编库数据失败:", err)
        compilationTableData.value = [] // 请求失败时设为空数组
      })
    })
  }
  // 定义定时器变量
  let pollingTimer = null;
  const getTableList = async () => {
    loading.value = true
    try {
      if (createdTime.value) {
        searchForm.startTime = createdTime.value[0]
        searchForm.endTime = createdTime.value[1]
      }
      columns.value = DEFAULT_COLUMNS;
      dataType.value = 'lx';
      // 关闭之前的轮询定时器
      if (pollingTimer) {
        clearInterval(pollingTimer);
        pollingTimer = null;
      }
      // 分域
      if (activeKey.value == '1') {
        const current = treeState.currentNode;
        if (current) {
          if (current.type == 'other' && current.name == '离线数据') {
            searchForm.systemId = current.systemId;
            console.log('searchForm', searchForm);
            const params = {
              pagingSort: {
                currentPage: pagination.value.current,
                pageSize: pagination.value.pageSize,
              }, sortList: [], ...searchForm
            }
            fileApi.getFileList(params).then(res => {
              data.value = res.data.records
              pagination.value.total = res.total
            })
          } else if (current?.type && current.type != 'other') {

            // 启动轮询，每3秒执行一次
            pollingTimer = setInterval(async () => {
              await getSSBWData();
            }, 3000);
            // 立即执行一次
            await getSSBWData();
          }
        }
      } else {
        searchForm.systemId = undefined;
        searchForm.organizeId = treeStateOrg.selectedKeys[0]
        console.log('searchForm', searchForm);
        const params = { pagingSort: {}, sortList: [], ...searchForm }
        fileApi.getFileList(params).then(res => {
          data.value = res.data.records
          pagination.value.total = res.total
        })
      }

    } catch (error) {
      console.error('获取文件列表失败:', error)
      message.error('获取文件列表失败')
    } finally {
      setTimeout(() => {
        loading.value = false
      }, 300);
    }
  }
  const download = (type: string) => {
    // 提示，一次最多下载10000条
    Modal.confirm({
      title: '提示',
      content: '一次最多下载10000条，是否继续？',
      onOk: async () => { handleDownload(type) },
      onCancel: () => { },
    })
    const handleDownload = async (type: any) => {
      // 下载文件
      try {
        const params = {
          pagingSort: {
            currentPage: 1,
            pageSize: 10000,
          },
          tableName: type == 'origin' ? treeState.currentNode.name : `dwd_${treeState.currentNode.name}`,
          startTime: undefined,
          endTime: undefined,
        }
        if (type == 'origin' && createdTimeSSWB.value) {
          params.startTime = createdTimeSSWB.value[0]
          params.endTime = createdTimeSSWB.value[1]
        } else if (type == 'dwd' && createdTimeSSWBDwd.value) {
          params.startTime = createdTimeSSWBDwd.value[0]
          params.endTime = createdTimeSSWBDwd.value[1]
        }
        const response = await axios.post(`/api/data/download`, params, {
          responseType: 'blob', // 设置响应类型为 blob
          headers: {
            'Authorization': localStorage.getItem("token")
          }
        })
        console.log('response', response);
        const fileName = response.headers['content-disposition'].split('filename=')[1]
        const blob = new Blob([response.data], { type: 'application/octet-stream' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = decodeURIComponent(fileName)
        link.click()
        URL.revokeObjectURL(link.href)
        message.success('下载成功')
      } catch (error) {
        console.error('下载文件失败:', error)
        message.error('下载文件失败')
      }
    }
  }
  const changePagination = (pageInfo) => {
    console.log('pageInfo.current', pageInfo.current)
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    getTableList();
  }
  const changePaginationSSBW = (pageInfo) => {
    console.log('pageInfo.current', pageInfo.current)
    paginationsswb.value.current = pageInfo.current;
    paginationsswb.value.pageSize = pageInfo.pageSize;
    getSSBWData();
  }
  const changeDwdPagination = (pageInfo) => {
    dwdPagination.value.current = pageInfo.current;
    dwdPagination.value.pageSize = pageInfo.pageSize;
    getSSBWData();
  }      // 下载文件
  const downloadFile = async (file) => {
    try {
      message.success(`开始下载文件：${file.name}`)
      // 使用 axios 进行文件下载，以便处理响应头
      const response = await axios.get(`/api/files/download/${file.id}`, {
        responseType: 'blob', // 设置响应类型为 blob
        headers: {
          'Authorization': localStorage.getItem("token")
        }
      })

      // 创建 Blob URL
      const blob = new Blob([response.data], {
        type: response.headers['content-type']
      })
      const url = window.URL.createObjectURL(blob)
      // 创建一个隐藏的a标签来处理下载
      const link = document.createElement('a')
      link.href = url
      // 使用 encodeURIComponent 对文件名进行编码
      link.download = decodeURIComponent(file.name)
      document.body.appendChild(link)
      link.click()

      // 清理资源
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载文件错误:', error)
      message.error(`下载失败: ${error.message || '未知错误'}`)
    }
  }

  // 收藏/取消收藏文件
  const follow = async (record) => {
    if (!record || !record.id) {
      message.error('文件信息不完整')
      return
    }

    try {
      // 切换收藏状态 (1-收藏, 0-未收藏)
      const isImportant = record.isImportant ? 0 : 1
      const response = await fileApi.setImportant(record.id, isImportant)
      if (response.code == 200) {
        message.success('操作成功')
        getTableList()
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      message.error('操作失败')
    }
  }


  const { tableHeight, change, loading } = tableMixin({
    getTableList
  });
</script>

<template>
  <a-config-provider>
    <div class="main">
      <div class="left">
        <a-tabs class="custom-tabs" v-model:activeKey="activeKey" size="small">
          <a-tab-pane key="1" tab="分域">
            <!-- <div class="left-head">
              <a-input placeholder="请输入名称进行检索" v-model:value="state.keywords" @pressEnter="handle('search')"
                @change="handle('search')" allow-clear>
                <template #prefix>
                  <SvgIcon name="search" />
                </template>
              </a-input>
            </div> -->
            <div class="left-tree">
              <ab-tree ref="treeRef" :fieldNames="treeFieldNames" v-model:expandedKeys="treeState.expandedKeys"
                v-model:selectedKeys="treeState.selectedKeys" :treeData="treeState.list" showIcon blockNode
                :load-data="onLoadTreeData" @select="selectTreeNode">
                <template #title="{ dataRef }">
                  <span class="tree-node-title" :title="dataRef.name">{{ dataRef.name }}</span>
                </template>
                <template #icon="{ dataRef }">
                  <SvgIcon v-if="dataRef.type === contentEnum.detail" name="modelIcon" className="tree-node-icon" />
                  <SvgIcon v-else className="tree-node-icon" name="folder" />
                </template>
                <template #nodeAction="{ record }">
                  <a-dropdown v-if="record.type !== 'other'" class="model-tree-action-dropdown">
                    <span class="ant-dropdown-link">
                      <MoreOutlined />
                    </span>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item>
                          <a @click.stop="onSubcribe(record, 1)">订阅</a>
                        </a-menu-item>
                        <a-menu-item>
                          <a @click.stop="onSubcribe(record, 0)">取消订阅</a>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>
              </ab-tree>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="container">
        <breadcrumb></breadcrumb>
        <div class="content">
          <div class="page-content" v-show="dataType=='lx'">
            <div class="page-filters">
              <!-- 第二行栅格：查询条件和按钮 -->
              <a-row :gutter="16">
                <a-col :span="6">
                  <label class="filter-item">
                    <label class="filter-label">文件名：</label>
                    <a-input class="user-input" v-model:value="searchForm.name" placeholder="请输入" style="flex: 1;" />
                  </label>
                </a-col>
                <a-col :span="6">
                  <label class="filter-item">
                    <label class="filter-label">上传人：</label>
                    <a-input v-model:value="searchForm.username" placeholder="请输入" style="flex: 1;" />
                  </label>
                </a-col>

                <a-col :span="6">
                  <label class="filter-item">
                    <label class="filter-label">创建时间：</label>
                    <a-range-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="createdTime"
                      :placeholder="['开始日期', '结束日期']" style="flex: 1;" />
                  </label>
                </a-col>
                <a-col :span="6">
                  <a-space :size="8"> <!-- 按钮右对齐 -->
                    <a-button type="primary" @click="filtersClick('search')">查询</a-button>
                    <a-button @click="filtersClick('reset')">重置</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>
            <ab-spin size="large" v-show="loading"></ab-spin>
            <div class="page-main">
              <a-table v-show="!loading" class="normal-table-component" :columns="columns"
                :scroll="{ y: data.length === 0 ? 'auto' : tableHeight }" :data-source="data" :bodered="false"
                :pagination="pagination" @change="changePagination"
                :style="{ height: data.length === 0 ? '100%' : '100%' }">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.dataIndex === 'index'">
                    {{ index + 1 }}
                  </template>
                  <template v-if="column.dataIndex === 'operate'">
                    <div class="operate">
                      <div class="operateIcon icon iconfont FCstar-fill"
                        :style="{ color: record.isImportant === 1 ? '#F79009' : '' }" @click="follow(record)">
                      </div>
                      <a-divider type="vertical" style="border-color:#333741" />
                      <div class="operateIcon icon iconfont FCdownload-2-fill" @click="downloadFile(record)">
                      </div>
                    </div>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
          <div v-show="dataType=='ssbw'" class="page-content-ssbw">
            <a-tabs v-model:activeKey="showActiveKey" size="small">
              <a-tab-pane key="1" tab="原始库">
                <div class="page-filters-ssbw">
                  <a-button type="primary" @click="download('origin')"> <upload-outlined></upload-outlined>下载</a-button>
                  <a-space>
                    <label class="filter-label">创建时间：</label>
                    <a-range-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="createdTimeSSWB"
                      :placeholder="['开始日期', '结束日期']" style="flex: 1;" />
                    <a-button type="primary" @click="filtersClickSSBW('search')">查询</a-button>
                    <a-button @click="filtersClickSSBW('reset')">重置</a-button>
                  </a-space>
                </div>
                <ab-spin size="large" v-show="loading"></ab-spin>
                <a-table v-show="!loading" class="detail-table-component" :columns="columns" :data-source="tableData"
                  :pagination="paginationsswb"
                  :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
                  :scroll="{ y: tableHeight, x: columns.length*150 }" @change="changePaginationSSBW">
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="2" tab="整编库">
                <div class="page-filters-ssbw">
                  <a-button type="primary" @click="download('dwd')"> <upload-outlined></upload-outlined>下载</a-button>
                  <a-space>
                    <label class="filter-label">创建时间：</label>
                    <a-range-picker showTime valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="createdTimeSSWBDwd"
                      :placeholder="['开始日期', '结束日期']" style="flex: 1;" />
                    <a-button type="primary" @click="filtersClickSSBW('search')">查询</a-button>
                    <a-button @click="filtersClickSSBW('reset','true')">重置</a-button>
                  </a-space>
                </div>
                <ab-spin size="large" v-show="loading"></ab-spin>
                <a-table v-show="!loading" class="detail-table-component" :columns="columns"
                  :data-source="compilationTableData" :pagination="dwdPagination"
                  :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
                  :scroll="{ y: tableHeight, x: columns.length*150 }" @change="changeDwdPagination">
                </a-table>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </div>
    </div>
  </a-config-provider>
</template>

<style scoped lang="less">
  @import './sytle.less';

  .page-content-ssbw {
    height: 100%;
  }

  .page-content {
    height: calc(100% - 32px - 16px);
  }

  .ant-tabs .ant-tabs-tabpane {
    display: flex;
    flex-direction: column;
  }

  /* 添加树节点标题样式 */
  :deep(.ant-tree-node-content-wrapper) {
    overflow: hidden;
  }

  :deep(.base-tree-content) {
    padding-right: 0;
  }

  :deep(.base-tree-node-title) {
    display: inline-block;
    max-width: calc(100% - 28px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }

  /* :deep(.ant-tabs.ant-tabs-top .ant-tabs-tab){
    padding: 0;
  } */
  :deep(.ant-tabs-nav) {
    padding: 0 12px;

  }

  .page-filters {
    width: 100%;
    margin-bottom: 16px;
  }

  .filter-item {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  /* 统一标签样式（可选） */
  .page-filters-ssbw {
    width: 100%;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
  }

  .page-filters .ant-space {
    width: 100%;

    label {
      width: auto;
      display: inline-block;
    }
  }

  /* 统一表单元素宽度（可选） */
  .page-filters .ant-select,
  .page-filters .ant-select-selector,
  .page-filters .ant-input,
  .page-filters .ant-picker {
    width: 100%;
  }

  /* 统一标签样式 */
  .filter-label {
    /* 设置标签最小宽度 */
    text-align: left;
    /* 标签文字右对齐 */
    font-weight: 500;
    white-space: nowrap;
    /* 防止标签文字换行 */
  }

  /* 统一表单元素样式 */
  .ant-select,
  .ant-input,
  .ant-picker {
    min-width: 0;
    /* 修复某些情况下flex无法收缩的问题 */
  }
</style>