<script setup>
  import { ref, onMounted, defineProps, watch, nextTick } from 'vue'
  import bussinessApi from '@/api/bussiness/bussiness.api';
  import ProtocolApi from '@/api/protocol/protocol.api';
  import { message } from 'ant-design-vue';

  const props = defineProps({
    id: { required: true },
    type: { required: true }
  });
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
  });
  const tableHeight = ref(600)
  const columns = ref([
    {
      title: '序号',
      key: 'index',
      width: 60,
      align: 'center',
      fixed: 'left',
      customRender: ({ text, record, index }) => {
        return index + 1;
      },
    },
    {
      title: '表名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true
    },
    {
      title: '指令名称',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 150,
      ellipsis: true
    },
    {
      title: '发送方',
      dataIndex: 'sender',
      key: 'sender',
      width: 120,
      ellipsis: true
    },
    {
      title: '接收方',
      dataIndex: 'receiver',
      key: 'receiver',
      width: 120,
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center'
    },
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      width: 80,
      fixed: 'right',
      align: 'center'
    },
  ])
  const tableData = ref([])
  const changePagination = (pageInfo) => {
    pagination.value.current = pageInfo.current;
    pagination.value.pageSize = pageInfo.pageSize;
    getSystemProtocols(props.id)
  }
  function getSystemProtocols(id) {
    try {
      loading.value = true
      const type = sessionStorage.getItem('state.type') || '1';
      console.log('typetypetypetype', type);
      const params = {
        systemId: type == '2' ? id : undefined,
        domainId: type == '1' ? id : undefined
      }
      const body = {
        pagingSort: {
          currentPage: pagination.value.current,
          pageSize: pagination.value.pageSize
        }
      }
      ProtocolApi.getProtocolPage(params, body).then(res => {
        tableData.value = res.data.records
        pagination.value.total = res.data.total
        loading.value = false
      }).catch(err => {
        console.error('err', err);
        loading.value = false
      })
    } catch (err) {
      console.error('err', err);
      loading.value = false
    }
  }

  function onSubcribe(record) {
    const params = {
      status: 1,
      protocolId: record.id
    }
    ProtocolApi.subscribeProtocol(params).then(res => {
      getSystemProtocols(props.id)
    })
  }
  function onStop(record) {
    const params = {
      status: 0,
      protocolId: record.id
    }
    ProtocolApi.subscribeProtocol(params).then(res => {
      getSystemProtocols(props.id)
    })
  }

  watch(() => props.id, (val) => {
    console.log('props.id', props.id);

    getSystemProtocols(val)
  }, { immediate: true })

  onMounted(() => {
    window.onresize = () => {
      tableHeight.value = document.getElementsByClassName('page-main')[0].offsetHeight - 48 - 48;
    };
    // nextTick(() => {
      tableHeight.value = document.getElementsByClassName('page-main')[0].offsetHeight - 48 - 48;
  //  })
  });
</script>
<template>
  <div class="tableTitle">主题列表</div>

  <div class="page-main table">
    <ab-spin size="large" v-show="loading"></ab-spin>
    <a-table v-show="!loading" class="normal-table-component" :columns="columns" @change="changePagination"
      :data-source="tableData" :pagination="pagination"
      :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
      :scroll="{ y: tableData.length === 0 ? 'auto' : tableHeight }"
      :style="{ height: tableData.length === 0 ? '100%' : '100%' }">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key === 'status'">
          <a-badge v-if="text" status="success" text="订阅中" />
          <a-badge v-else status="error" text="断开" />
        </template>
        <template v-if="column.key === 'operate'">
          <div v-if="record.status" class="operate-button" @click.stop="onStop(record)">断开</div>
          <div v-else class="operate-button" @click.stop="onSubcribe(record)">订阅</div>
        </template>
      </template>
    </a-table>
  </div>
</template>
<style lang="less" scoped>
  @import '../sytle.less';

  .page-main {
    height: calc(100% - 40px);
  }

  .operate-button {
    cursor: pointer;
  }

  .table .normal-table-component {
    height: calc(100% - 64px);
  }
</style>