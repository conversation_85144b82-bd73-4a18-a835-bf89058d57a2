#app {
  width: 100%;
  height: 100%;
}


.ant-table-wrapper .ant-table-cell-scrollbar:not([rowspan]) {
  box-shadow: none;
}

::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

::-webkit-scrollbar-track {
  background-color: #161B26 !important;
}

::-webkit-scrollbar-thumb {
  background-color: #333741 !important;
  border-radius: 10px !important;
  border: 3px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  border: unset !important;
  background-color: #61646C !important;
}

::-webkit-scrollbar-thumb:active {
  background-color: #61646C !important;
}

.scrollbar {
  width: 12px;
  background-color: #161B26 !important;

  .slider {
      width: 6px;
      border-radius: 10px;
      left: 3px;
      background-color: #333741 !important;

      &:hover {
          left: 0px !important;
          right: 0;
          width: 12px !important;
          border-radius: 10px;
      }
  }
}


.statistics{
  /* width: 100%; */
  /* border: 1px solid #458EFF; */
  border-radius: 4px;
  /* padding: 16px; */
}
.tableTitle{
  margin-bottom: 16px;
  color: #FFF;
  font-family: "Microsoft YaHei";
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 22px; /* 157.143% */
  letter-spacing: 1.12px;
}

.detail{
  width: 100%;
  height: calc(100% - 174px);
  margin-top: 24px;
  .ant-table-wrapper {
    height: 100% !important;
  }
}
