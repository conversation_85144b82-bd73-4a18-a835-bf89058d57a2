import {
  AuditOutlined,
  RadarChartOutlined
} from '@ant-design/icons-vue'
import { h } from 'vue'
const menuList: any[] = [
  {
    key: '/CHSJ/index',
    icon: () => h('i', { class: 'iconfont FCsuanfamoxingguanli' }),
    label: '筹划设计阶段',//筹划设计阶段
  },
  {
    key: '/FZTY/index',
    icon: () => h('i', { class: 'iconfont FCtimeline-view' }),
    label: '仿真推演阶段',//仿真推演阶段
  },
  {
    key: '/data',
    icon: () => h('i', { class: 'iconfont FCmobanguanli' }),
    label: '数实对抗阶段',//数实对抗阶段
    children: [
      {
        key: '/data/data',
        label: '实时数据',
      },
      {
        key: '/data/offlineFile',
        label: '离线文件',
      },
      
      {
        key: '/data/meteorologicalData',
        label: '人工填报气象数据',
      },
    ]
  },
  {
    key: '/FPPG/index',
    icon: () => h('i', { class: 'iconfont FCbookmark' }),
    label: '复盘评估阶段',
  }
]
// 查询共享
const menuList_share: any[] = [
  {
    key: '/comprehensiveQuery/index',
    icon: () => h('i', { class: 'iconfont FCmenu-search' }),
    label: '综合查询',
  },
  {
    key: '/query',
    icon: () => h('i', { class: 'iconfont FCmobanguanli' }),
    label: '查询方式1',
    children: [
      {
        key: '/query/queryCHSJ',
        label: '筹划设计阶段',//筹划设计阶段
      },
      {
        key: '/query/queryFZTY',
        label: '仿真推演阶段',//仿真推演阶段
      },
      {
        key: '/query/querySSDK',
        label: '数实对抗阶段',//数实对抗阶段
      },
      {
        key: '/query/queryFPPG',
        label: '复盘评估阶段',
      },
    ]
  },
  {
    key: '/otherQuery/index',
    icon: () => h('i', { class: 'iconfont FCbookmark' }),
    label: '查询方式2',
  }
]
export {
  menuList,
  menuList_share
}
