
import ENV from '../config'
/**
 * 删除嵌套数组内id等于指定id的节点
 * @param arr 
 * @param id 
 * @returns 
 */
function removeTreeNodeByKey(arr: any[], id: any) {
  const result = []
  for (const item of arr) {
    if (item.id !== id) {
      if (item.children && Array.isArray(item.children)) {
        item.children = removeTreeNodeByKey(item.children, id)
      }
      result.push(item);
    }
  }
  return result
}

/**
 * 获取嵌套数组的key
 * @param arr 
 */
function getTreeNodeKey(arr: any[]) {
  const result = []
  for (const item of arr) {
    if (item.children && Array.isArray(item.children)) {
      const arr: any = getTreeNodeKey(item.children)
      result.push(...arr)
    }
    result.push(item.id);
  }
  return result
}



/**
 * 添加添加cdn路径
 * @param path 
 * @param metaUrl  import.meta.url
 * @returns 
 */
const addCdnUrl = (path: string): string => {
  let dynamicCdn = ''
  if (process.env.NODE_ENV == "production") {
    if (window["cdnUrl"]) {
      ENV.assetsUrl = dynamicCdn = window["cdnUrl"] + "/trainManagement/";
    } else {
      console.log(
        "%c cdn地址获取异常",
        "background:#fff ; padding: 10px; border-radius: 3px;  color: red;font-size:50px;"
      );
      ENV.assetsUrl = dynamicCdn =
        "http://frontend.amberdata.cn:30080/trainManagement/";
    }
  }

  return path.replace('http://frontend.amberdata.cn:30080', dynamicCdn)
}

export { removeTreeNodeByKey, getTreeNodeKey, addCdnUrl }
