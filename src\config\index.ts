// const { name } = require('../../package.json');


const ENV = {
  // serverAddress: 'http://localhost:2222', // 后台服务地址
  serverAddress: '',
  enableCSRF: false, // 是否打开Crsf校验
  enableVerifyPass: true,
  useTokenAuthentication: false,
  previewUrl:
    process.env.NODE_ENV == 'production'
      ? `//${window.location.host}/ecmcore/docview/`
      : `//*************/docview`,
  baseUrl: 'http://*************:8090/',
  adminApi: '/adminapi', //API请求代理标识
  devLoginUrl: '//localhost:4200/?goto=' + encodeURIComponent(window.location.href),
  proLoginUrl: `${window.location.origin}/adminapi/pub/userNotLogin.html?goto=${encodeURIComponent(window.location.href)}`,
  train: '/train',
  systemApi: '/systemapi'
}

export default ENV
