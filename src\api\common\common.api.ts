import request from '@/common/request'
import { ApiUrl } from './common.url'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'
// import { axiosIns } from '@/common/ajax'

import ENV from '@/config'

const adminApi: string = ENV.adminApi

class CommonApi {
  /**
   * 上传
   * @param data
   * @returns
   */
  fileUpload = (data: any) => {
    // return (<any>axiosIns.post(`/common/file_upload`, data, {
    //   headers: { "Content-Type": "multipart/form-data" },
    //   responseType: 'application/json'
    // }))
    return
  }

  /**
   * 登出
   * @returns
   */
  logOut = () => {
    //  logOut = (): Promise<any> => {
    // return (<any>axiosIns.get(`/login_out`)).then(() => {
    //   window.location.reload()
    //   localStorage.clear()
    //   sessionStorage.clear()
    // }).catch(() => {
    //   window.location.reload()
    // })
    return
  }

  /**
   *  获取用户信息
   * @param data
   * @returns
   * grade:1 超级管理员 2 租户管理员 3 普通用户
   */
  getCurrentUser = () => request.get(adminApi + ApiUrl.getCurrentUser)

  getCurrentLoginConfig = () =>
    request.post(adminApi + ApiUrl.getCurrentLoginConfig, {
      columns: [],
      paging: {
        currentPage: 1,
        endIndex: 0,
        pageSize: 999,
        startIndex: 0,
        totalCount: 0
      },
      sortList: []
    })
}
const commonApi = new CommonApi()
export default commonApi
